import { format, formatDistance, formatRelative, isValid, parse, parseISO } from 'date-fns';
import { vi } from 'date-fns/locale';

/**
 * Các hàm tiện ích để định dạng ngày tháng với timezone Việt Nam
 */

/**
 * Định dạng thời gian với timezone Việt Nam
 * @param date Ngày cần định dạng
 * @param formatStr Chuỗi định dạng (mặc định: 'dd/MM/yyyy HH:mm')
 * @returns Chuỗi đã được định dạng
 */
export function formatDateTimeVN(
  date: Date | string | number | undefined | null,
  formatStr: string = 'dd/MM/yyyy HH:mm'
): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

    if (!isValid(dateObj)) return '';

    // <PERSON><PERSON> dụng toLocaleString với timezone Việt Nam
    if (formatStr === 'dd/MM/yyyy HH:mm') {
      return dateObj.toLocaleString('vi-VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }

    // Fallback cho các format khác
    return format(dateObj, formatStr, { locale: vi });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Định dạng ngày với timezone Việt Nam
 * @param date Ngày cần định dạng
 * @returns Chuỗi đã được định dạng (dd/MM/yyyy)
 */
export function formatDateVN(
  date: Date | string | number | undefined | null
): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

    if (!isValid(dateObj)) return '';

    return dateObj.toLocaleDateString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Định dạng ngày tháng
 * @param date Ngày cần định dạng
 * @param formatStr Chuỗi định dạng (mặc định: 'dd/MM/yyyy')
 * @param locale Locale để định dạng (mặc định: vi)
 * @returns Chuỗi đã được định dạng
 */
export function formatDate(
  date: Date | string | number | undefined | null,
  formatStr: string = 'dd/MM/yyyy',
  locale = vi
): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

    if (!isValid(dateObj)) return '';

    return format(dateObj, formatStr, { locale });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Định dạng ngày tháng với giờ
 * @param date Ngày cần định dạng
 * @param formatStr Chuỗi định dạng (mặc định: 'dd/MM/yyyy HH:mm')
 * @param locale Locale để định dạng (mặc định: vi)
 * @returns Chuỗi đã được định dạng
 */
export function formatDateTime(
  date: Date | string | number | undefined | null,
  formatStr: string = 'dd/MM/yyyy HH:mm',
  locale = vi
): string {
  return formatDate(date, formatStr, locale);
}

/**
 * Định dạng ngày tháng tương đối
 * @param date Ngày cần định dạng
 * @param baseDate Ngày cơ sở (mặc định: new Date())
 * @param locale Locale để định dạng (mặc định: vi)
 * @returns Chuỗi đã được định dạng
 */
export function formatRelativeDate(
  date: Date | string | number | undefined | null,
  baseDate: Date = new Date(),
  locale = vi
): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

    if (!isValid(dateObj)) return '';

    return formatRelative(dateObj, baseDate, { locale });
  } catch (error) {
    console.error('Error formatting relative date:', error);
    return '';
  }
}

/**
 * Định dạng khoảng thời gian
 * @param date Ngày cần định dạng
 * @param baseDate Ngày cơ sở (mặc định: new Date())
 * @param locale Locale để định dạng (mặc định: vi)
 * @returns Chuỗi đã được định dạng
 */
export function formatDistanceDate(
  date: Date | string | number | undefined | null,
  baseDate: Date = new Date(),
  locale = vi
): string {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

    if (!isValid(dateObj)) return '';

    return formatDistance(dateObj, baseDate, { locale, addSuffix: true });
  } catch (error) {
    console.error('Error formatting distance date:', error);
    return '';
  }
}

/**
 * Phân tích chuỗi ngày tháng
 * @param dateStr Chuỗi ngày tháng
 * @param formatStr Chuỗi định dạng (mặc định: 'dd/MM/yyyy')
 * @param locale Locale để phân tích (mặc định: vi)
 * @returns Đối tượng Date
 */
export function parseDate(
  dateStr: string,
  formatStr: string = 'dd/MM/yyyy',
  locale = vi
): Date {
  try {
    const date = parse(dateStr, formatStr, new Date(), { locale });

    if (!isValid(date)) {
      throw new Error('Invalid date');
    }

    return date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return new Date();
  }
}

/**
 * Lấy ngày đầu tiên của tháng
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @returns Ngày đầu tiên của tháng
 */
export function getFirstDayOfMonth(date: Date = new Date()): Date {
  return new Date(date.getFullYear(), date.getMonth(), 1);
}

/**
 * Lấy ngày cuối cùng của tháng
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @returns Ngày cuối cùng của tháng
 */
export function getLastDayOfMonth(date: Date = new Date()): Date {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}

/**
 * Lấy ngày đầu tiên của năm
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @returns Ngày đầu tiên của năm
 */
export function getFirstDayOfYear(date: Date = new Date()): Date {
  return new Date(date.getFullYear(), 0, 1);
}

/**
 * Lấy ngày cuối cùng của năm
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @returns Ngày cuối cùng của năm
 */
export function getLastDayOfYear(date: Date = new Date()): Date {
  return new Date(date.getFullYear(), 11, 31);
}

/**
 * Lấy ngày đầu tiên của tuần
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @param weekStartsOn Ngày bắt đầu tuần (0: Chủ nhật, 1: Thứ hai, ...) (mặc định: 1)
 * @returns Ngày đầu tiên của tuần
 */
export function getFirstDayOfWeek(date: Date = new Date(), weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1): Date {
  const day = date.getDay();
  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;

  return new Date(date.getFullYear(), date.getMonth(), date.getDate() - diff);
}

/**
 * Lấy ngày cuối cùng của tuần
 * @param date Ngày tham chiếu (mặc định: new Date())
 * @param weekStartsOn Ngày bắt đầu tuần (0: Chủ nhật, 1: Thứ hai, ...) (mặc định: 1)
 * @returns Ngày cuối cùng của tuần
 */
export function getLastDayOfWeek(date: Date = new Date(), weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6 = 1): Date {
  const firstDay = getFirstDayOfWeek(date, weekStartsOn);
  return new Date(firstDay.getFullYear(), firstDay.getMonth(), firstDay.getDate() + 6);
}

/**
 * Kiểm tra xem một ngày có phải là ngày hôm nay không
 * @param date Ngày cần kiểm tra
 * @returns true nếu là ngày hôm nay, false nếu không phải
 */
export function isToday(date: Date | string | number): boolean {
  const today = new Date();
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

  return (
    dateObj.getDate() === today.getDate() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getFullYear() === today.getFullYear()
  );
}

/**
 * Kiểm tra xem một ngày có phải là ngày trong tương lai không
 * @param date Ngày cần kiểm tra
 * @returns true nếu là ngày trong tương lai, false nếu không phải
 */
export function isFutureDate(date: Date | string | number): boolean {
  const today = new Date();
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

  today.setHours(0, 0, 0, 0);
  dateObj.setHours(0, 0, 0, 0);

  return dateObj > today;
}

/**
 * Kiểm tra xem một ngày có phải là ngày trong quá khứ không
 * @param date Ngày cần kiểm tra
 * @returns true nếu là ngày trong quá khứ, false nếu không phải
 */
export function isPastDate(date: Date | string | number): boolean {
  const today = new Date();
  const dateObj = typeof date === 'string' ? parseISO(date) : new Date(date);

  today.setHours(0, 0, 0, 0);
  dateObj.setHours(0, 0, 0, 0);

  return dateObj < today;
}
