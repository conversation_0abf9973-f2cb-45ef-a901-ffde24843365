import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { SaveIcon, X, Search, Plus } from "lucide-react";
import { toast } from "sonner";
import { CustomerForm } from "@/components/customers/CustomerForm";
import orderService from "@/services/orderService";
import apiClient from "@/lib/apiClient";

// Định nghĩa interface cho khách hàng
interface Customer {
  id: number;
  name: string;
  phone: string;
  card_number?: string;
}

// Đ<PERSON>nh nghĩa interface cho thẻ khách hàng hiển thị
interface CustomerCard {
  id: string;
  number: string;
  customer: string;
}

interface Order {
  id: string;
  cardId: string;
  customerName: string;
  amount: string;
  type: string;
  fee: string;
  totalDebt: string;
  note: string;
  customerFee?: string;
  negativeAmount?: string;
}

interface CustomerFormProps {
  onClose: () => void;
  onSuccess: () => void;
}

interface OrderFormProps {
  orderId?: string; // Nếu có orderId thì là edit, không có thì là tạo mới
  onCancel: () => void;
}

export function OrderForm({ orderId, onCancel }: OrderFormProps) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [cards, setCards] = useState<any[]>([]);
  const [order, setOrder] = useState<Order>({
    id: orderId || "",
    cardId: "",
    customerName: "",
    amount: "",
    type: "Đáo",
    fee: "1.7",
    totalDebt: "0",
    note: "",
    customerFee: "",
    negativeAmount: ""
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [showCardResults, setShowCardResults] = useState(false);
  const [showAddCardDialog, setShowAddCardDialog] = useState(false);

  // Lấy danh sách khách hàng và thẻ từ API
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Lấy danh sách khách hàng
        const customersData = await orderService.getCustomers();
        setCustomers(customersData);
        
        // Lấy danh sách thẻ
        const cardsResponse = await apiClient.get('/api/cards?include=Customer');
        console.log('Dữ liệu thẻ từ API:', cardsResponse.data);
        
        const cardsData = cardsResponse.data.data || [];
        setCards(cardsData);
        
        // Nếu đang chỉnh sửa đơn hàng, không cần tạo mã mới
        if (!orderId) {
          // Lấy danh sách đơn hàng để tạo mã mới
          const ordersResponse = await orderService.getAllOrders();
          const orders = ordersResponse.data || [];
          
          // Tìm ID lớn nhất trong danh sách đơn hàng hiện có
          let maxId = 0;
          orders.forEach((order: any) => {
            // Kiểm tra xem order có id không
            if (order && order.id) {
              const id = parseInt(order.id);
              if (!isNaN(id) && id > maxId) {
                maxId = id;
              }
            }
          });
          
          // Tạo mã đơn hàng mới - sử dụng số ID tiếp theo
          const newOrderId = (maxId + 1).toString();
          setOrder(prev => ({ ...prev, id: newOrderId }));
        }
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu:", error);
        toast.error("Không thể tải dữ liệu từ server");
      }
    };
    
    fetchData();
  }, [orderId]);

  // Lấy dữ liệu đơn hàng khi chỉnh sửa
  useEffect(() => {
    if (orderId) {
      const fetchOrder = async () => {
        try {
          const orderData = await orderService.getOrderById(parseInt(orderId));
          if (orderData && orderData.items && orderData.items.length > 0) {
            // Lấy thông tin từ đơn hàng
            const item = orderData.items[0];
            setOrder({
              id: orderId,
              cardId: orderData.card_id?.toString() || "",
              customerName: orderData.customer?.name || "",
              amount: item.price.toString(),
              type: orderData.order_type || "Đáo",
              fee: orderData.fee_percentage?.toString() || "1.7",
              totalDebt: "0",
              note: orderData.note || "",
              customerFee: orderData.charge_customer_fee || "",
              negativeAmount: orderData.negative_amount || ""
            });
          }
        } catch (error) {
          console.error("Lỗi khi lấy thông tin đơn hàng:", error);
          toast.error("Không thể tải thông tin đơn hàng");
        }
      };
      
      fetchOrder();
    }
  }, [orderId]);

  // Lấy lại danh sách thẻ sau khi thêm mới
  const fetchCards = async () => {
    try {
      const cardsResponse = await apiClient.get('/api/cards?include=Customer');
      const cardsData = cardsResponse.data.data || [];
      setCards(cardsData);
    } catch (error) {
      console.error("Lỗi khi lấy danh sách thẻ:", error);
      toast.error("Không thể tải danh sách thẻ");
    }
  };

  // Lọc thẻ dựa trên từ khóa tìm kiếm
  const filteredCards = cards.filter(card => {
    // Kiểm tra nếu có searchTerm
    if (!searchTerm.trim()) return false;
    
    // Tìm kiếm theo số thẻ
    const cardNumberMatch = card.card_number?.toString().toLowerCase().includes(searchTerm.toLowerCase());
    
    // Tìm kiếm theo tên khách hàng
    const customerNameMatch = card.Customer?.name?.toString().toLowerCase().includes(searchTerm.toLowerCase());
    
    // Tìm kiếm theo tên ngân hàng
    const bankNameMatch = card.bank_name?.toString().toLowerCase().includes(searchTerm.toLowerCase());
    
    // Trả về true nếu có bất kỳ kết quả nào khớp
    return cardNumberMatch || customerNameMatch || bankNameMatch;
  });
  
  // Ghi log kết quả lọc thẻ
  useEffect(() => {
    if (searchTerm.trim()) {
      console.log(`Tìm kiếm với từ khóa "${searchTerm}" - Tìm thấy ${filteredCards.length} thẻ`);
    }
  }, [searchTerm, filteredCards.length]);

  // Xử lý chọn thẻ
  const handleCardSelect = (card: any) => {
    try {
      // Lấy ID của thẻ và tên khách hàng
      const cardId = card.id.toString();
      const customerName = card.Customer?.name || "";
      
      // Cập nhật state của đơn hàng
      setOrder({
        ...order,
        cardId: cardId,
        customerName: customerName,
      });
      
      // Cập nhật ô tìm kiếm và đóng kết quả
      setSearchTerm(card.card_number);
      setShowCardResults(false);
      
      console.log(`Đã chọn thẻ: ${card.card_number}, Khách hàng: ${customerName}, ID: ${cardId}`);
    } catch (error) {
      console.error("Lỗi khi chọn thẻ:", error);
      console.log("Dữ liệu thẻ:", card);
      toast.error("Có lỗi khi chọn thẻ. Vui lòng thử lại.");
    }
  };
  
  // Xử lý khi thêm khách hàng và thẻ mới thành công
  const handleCustomerFormSuccess = async (customer: any) => {
    try {
      // Đóng dialog
      setShowAddCardDialog(false);
      
      // Refresh danh sách thẻ
      const cardsResponse = await apiClient.get('/api/cards?include=Customer');
      const cardsData = cardsResponse.data.data || [];
      setCards(cardsData);
      
      // Nếu có thẻ, chọn thẻ đầu tiên
      if (customer.cards && customer.cards.length > 0) {
        const firstCard = cardsData.find((c: any) => c.card_number === customer.cards[0].number);
        
        if (firstCard) {
          handleCardSelect(firstCard);
          toast.success(`Đã thêm thẻ mới và chọn thẻ ${firstCard.card_number}`);
        } else {
          toast.success("Thêm khách hàng và thẻ mới thành công");
        }
      } else {
        toast.success("Thêm khách hàng mới thành công");
      }
    } catch (error) {
      console.error("Lỗi khi xử lý thêm thẻ mới:", error);
      toast.error("Có lỗi khi xử lý thêm thẻ mới");
    }
  };

  // Tính toán phí
  const calculateFee = () => {
    if (!order.amount || !order.fee) return "0";
    // Chuyển đổi chuỗi số tiền thành số, loại bỏ dấu phẩy ngăn cách hàng nghìn
    const amount = Number(order.amount.replace(/\./g, "").replace(/,/g, ""));
    // Chuyển đổi chuỗi phần trăm thành số
    const feePercent = Number(order.fee);
    // Tính phí: Số tiền * Phí %
    const feeAmount = (amount * feePercent) / 100;
    // Trả về kết quả đã làm tròn và định dạng
    return Math.round(feeAmount).toLocaleString("vi-VN");
  };
  
  // Định dạng số tiền
  const formatCurrency = (value: string) => {
    // Loại bỏ tất cả các ký tự không phải số
    const numericValue = value.replace(/[^0-9]/g, '');
    
    // Nếu chuỗi rỗng, trả về rỗng
    if (!numericValue) return '';
    
    // Chuyển đổi thành số và định dạng theo tiền tệ Việt Nam
    return parseInt(numericValue).toLocaleString('vi-VN');
  };

  // Xử lý lưu đơn hàng
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Kiểm tra dữ liệu đầu vào
      if (!order.cardId || !order.customerName) {
        toast.error("Vui lòng chọn thẻ và khách hàng");
        setLoading(false);
        return;
      }

      if (!order.amount || parseFloat(order.amount.replace(/,/g, '')) <= 0) {
        toast.error("Vui lòng nhập số tiền hợp lệ");
        setLoading(false);
        return;
      }

      if (!order.fee || parseFloat(order.fee) <= 0) {
        toast.error("Vui lòng nhập tỷ lệ phí hợp lệ");
        setLoading(false);
        return;
      }

      // Tính toán các giá trị cần thiết
      // Chuyển đổi số tiền từ định dạng 100.000.000 thành 100000000
      // Loại bỏ tất cả dấu chấm trong số tiền VND
      const amountStr = order.amount.replace(/\./g, '');
      const amount = parseFloat(amountStr);
      
      // Kiểm tra giới hạn của DECIMAL(15,2)
      if (amount > 9999999999999.99) {
        toast.error("Số tiền quá lớn, vượt quá giới hạn cho phép");
        setLoading(false);
        return;
      }
      
      const feePercent = parseFloat(order.fee);
      // Tính phí dựa trên phần trăm
      const feeAmount = (amount * feePercent / 100);
      // Tổng số tiền chính là số tiền nhập vào, không cộng thêm phí
      const totalAmount = amount;
      
      // Chuẩn bị dữ liệu để gửi đến API
      // Không gửi created_at và updated_at - để database tự động tạo với timezone đúng
      const orderData = {
        card_id: parseInt(order.cardId),
        status: "pending",
        note: order.note,
        fee_percentage: feePercent,
        order_type: order.type,
        // Chuyển đổi feeAmount thành chuỗi theo đúng interface Order
        charge_customer_fee: feeAmount.toString(),
        total_amount: totalAmount
      };

      console.log('Gửi dữ liệu đơn hàng:', orderData);

      // Gọi API để lưu đơn hàng
      if (orderId) {
        await orderService.updateOrder(parseInt(orderId), orderData);
        toast.success("Đơn hàng đã được cập nhật thành công");
        navigate(`/orders/${orderId}`);
      } else {
        const result = await orderService.createOrder(orderData);
        console.log('Kết quả tạo đơn hàng:', result);
        toast.success("Đơn hàng mới đã được tạo thành công");
        
        // Chuyển hướng đến trang chi tiết đơn hàng vừa tạo
        if (result && result.id) {
          navigate(`/orders/${result.id}`);
        } else {
          navigate("/orders");
        }
      }
    } catch (error: any) {
      console.error("Lỗi khi lưu đơn hàng:", error);
      const errorMessage = error.response?.data?.message || 
        error.message || 
        "Có lỗi xảy ra khi lưu đơn hàng. Vui lòng kiểm tra lại thông tin đã nhập.";
      
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>{orderId ? "Chỉnh sửa đơn hàng" : "Tạo đơn hàng mới"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="id">Mã đơn hàng</Label>
              <Input
                id="id"
                value={order.id}
                readOnly
                className="bg-muted/50"
              />
            </div>

            {/* Chỉ hiển thị các trường được phép chỉnh sửa khi chỉnh sửa đơn hàng */}
            {!orderId && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="card">Số thẻ</Label>
                  <div className="relative">
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Input
                          id="card"
                          value={searchTerm}
                          onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setShowCardResults(true);
                          }}
                          onFocus={() => setShowCardResults(true)}
                          onBlur={() => {
                            // Delay hiding results to allow for clicks
                            setTimeout(() => setShowCardResults(false), 200);
                          }}
                          placeholder="Tìm kiếm thẻ hoặc tên khách hàng"
                          className="pr-10"
                        />
                        <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setShowAddCardDialog(true)}
                        className="flex items-center gap-1"
                      >
                        <Plus className="h-4 w-4" />
                        Thêm thẻ
                      </Button>
                    </div>
                    {showCardResults && (
                      <div className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border">
                        {cards
                          .filter((card: any) => 
                            card.card_number.includes(searchTerm) || 
                            card.Customer?.name?.toLowerCase().includes(searchTerm.toLowerCase())
                          )
                          .map((card: any) => (
                            <div
                              key={card.id}
                              className="p-3 cursor-pointer hover:bg-gray-50"
                              onClick={() => handleCardSelect(card)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium">{card.card_number}</p>
                                  <p className="text-sm text-gray-500">{card.Customer?.name}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label htmlFor="amount">Tổng số tiền</Label>
              <Input
                id="amount"
                type="text"
                value={order.amount}
                onChange={(e) => setOrder(prev => ({ ...prev, amount: e.target.value }))}
                placeholder="Nhập tổng số tiền"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fee">Phí %</Label>
              <Input
                id="fee"
                type="text"
                value={order.fee}
                onChange={(e) => setOrder(prev => ({ ...prev, fee: e.target.value }))}
                placeholder="Nhập phí %"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customerFee">Tiền phí</Label>
              <Input
                id="customerFee"
                type="text"
                value={order.customerFee}
                onChange={(e) => setOrder(prev => ({ ...prev, customerFee: e.target.value }))}
                placeholder="Nhập tiền phí"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="negativeAmount">Tiền âm</Label>
              <Input
                id="negativeAmount"
                type="text"
                value={order.negativeAmount}
                onChange={(e) => setOrder(prev => ({ ...prev, negativeAmount: e.target.value }))}
                placeholder="Nhập tiền âm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="note">Ghi chú</Label>
              <Input
                id="note"
                type="text"
                value={order.note}
                onChange={(e) => setOrder(prev => ({ ...prev, note: e.target.value }))}
                placeholder="Nhập ghi chú"
              />
            </div>
          </div>

          <Dialog open={showAddCardDialog} onOpenChange={setShowAddCardDialog}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>Thêm thẻ mới</DialogTitle>
                <DialogDescription>
                  Nhập thông tin thẻ mới
                </DialogDescription>
              </DialogHeader>
              <CustomerForm 
                onCancel={() => setShowAddCardDialog(false)}
                onSuccess={() => {
                  setShowAddCardDialog(false);
                  // Cập nhật danh sách thẻ sau khi thêm thành công
                  fetchCards();
                }}
              />
            </DialogContent>
          </Dialog>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="mr-2 h-4 w-4" />
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            <SaveIcon className="mr-2 h-4 w-4" />
            {loading ? "Đang lưu..." : "Lưu đơn hàng"}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
}
