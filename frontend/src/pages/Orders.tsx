import { useEffect, useState, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "sonner";
import { Search, Filter, Edit, Trash2, Loader2 } from "lucide-react";

// Components
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Order components
import { OrderForm } from "@/components/orders/OrderForm";
import { OrderDetail } from "@/components/orders/OrderDetail";
import { OrderEditModal } from "@/components/orders/OrderEditModal";
import { CreateOrderButton } from "@/components/orders/CreateOrderButton";
import { OrderFilterModal } from "@/components/orders/OrderFilterModal";

// Services
import orderService, { Order as OrderServiceType } from "@/services/orderService";
import { getBankName, getBankList } from "@/services/bankService";
import { getOrderStats } from "@/services/statsService";
import { formatDateTimeVN } from "@/utils/date-format";

// Định nghĩa các kiểu đơn hàng
const ORDER_TYPES = ['Đáo', 'Rút', 'Trả phí'] as const;
type OrderType = typeof ORDER_TYPES[number];

// Định nghĩa interface cho đơn hàng
interface Order {
  id: number;
  customer_name: string;
  total_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
  fee_percentage?: number;
  order_type?: string;
  bank_name?: string;
  card?: {
    card_number: string;
    bank_name?: string;
  };
  customer?: {
    name: string;
  };
}

// Định nghĩa interface cho mặt hàng trong đơn hàng
interface OrderItem {
  id: number;
  product_id: number;
  quantity: number;
  price: number;
  total: number;
  product_name: string;
}

// Định nghĩa interface cho dữ liệu hiển thị
interface OrderDisplay {
  id: number;
  createdAt: string;
  status: string;
  customerName: string;
  customerId?: number; // Thêm trường customerId để lọc chính xác hơn
  total_amount: number;
  items_count: number;
  type: OrderType;
  totalSwiped: string;
  totalTransferred: string;
  totalFee: string;
  cardNumber: string;
  bank_name?: string;
  card_id?: number;
}

// Định nghĩa styles cho các trạng thái đơn hàng
const statusStyles: Record<string, { color: string; bgColor: string; borderColor: string; label: string }> = {
  "pending": {
    color: "text-slate-500",
    bgColor: "bg-slate-500/10",
    borderColor: "border-slate-500/20",
    label: "Chờ xử lý"
  },
  "processing": {
    color: "text-amber-500",
    bgColor: "bg-amber-500/10",
    borderColor: "border-amber-500/20",
    label: "Đang xử lý"
  },
  "completed": {
    color: "text-emerald-500",
    bgColor: "bg-emerald-500/10",
    borderColor: "border-emerald-500/20",
    label: "Hoàn thành"
  },
  "cancelled": {
    color: "text-rose-500",
    bgColor: "bg-rose-500/10",
    borderColor: "border-rose-500/20",
    label: "Đã hủy"
  },
};

// Hàm định dạng màu cho các loại đơn hàng
const typeStyles: Record<string, { color: string; bgColor: string; borderColor: string }> = {
  "Đáo": {
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    borderColor: "border-blue-500/20"
  },
  "Rút": {
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    borderColor: "border-purple-500/20"
  },
  "Trả phí": {
    color: "text-green-500",
    bgColor: "bg-green-500/10",
    borderColor: "border-green-500/20"
  }
};

// Define the type for view mode
type ViewMode = "list" | "detail" | "form";

export default function Orders() {
  const navigate = useNavigate();
  const params = useParams();
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [prevSearchTerm, setPrevSearchTerm] = useState("");
  const [orders, setOrders] = useState<OrderDisplay[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<OrderDisplay[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  // Không cần state showCreateModal nữa vì đã được xử lý trong CreateOrderButton
  const [editOrderId, setEditOrderId] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [banks, setBanks] = useState<any[]>([]);
  const [banksLoaded, setBanksLoaded] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filterCustomerName, setFilterCustomerName] = useState("all");
  const [filterCustomerId, setFilterCustomerId] = useState<number | undefined>();
  const [filterStartDate, setFilterStartDate] = useState<Date | undefined>();
  const [filterEndDate, setFilterEndDate] = useState<Date | undefined>();
  const [filterOrderType, setFilterOrderType] = useState("all");

  // State cho thống kê đơn hàng
  const [orderStats, setOrderStats] = useState({
    totalOrders: 0,
    totalAmount: 0,
    statusCounts: {
      pending: 0,
      processing: 0,
      completed: 0
    }
  });

  // Add page title
  useEffect(() => {
    document.title = "Quản lý đơn hàng | Tín Phát Credit";

    // Lấy thống kê đơn hàng
    fetchOrderStats();
  }, []);

  // Hàm lấy thống kê đơn hàng
  const fetchOrderStats = async () => {
    try {
      const response = await getOrderStats();
      if (response.success) {
        setOrderStats(response.data);
      }
    } catch (error) {
      console.error("Error fetching order stats:", error);
    }
  };

  // Lấy danh sách ngân hàng
  useEffect(() => {
    const fetchBanks = async () => {
      try {
        // Không đặt loading=true ở đây để tránh trễ giao diện
        const response = await getBankList();
        if (response.success) {
          console.log('Bank list data:', response.data);
          setBanks(Array.isArray(response.data) ? response.data : [response.data]);
          setBanksLoaded(true); // Đánh dấu là banks đã được tải xong
        }
      } catch (error) {
        console.error('Error fetching banks:', error);
        setBanksLoaded(true); // Vẫn đánh dấu là đã xong để không chặn việc tải orders
      }
    };

    fetchBanks();
  }, []);

  // Lấy danh sách đơn hàng từ API - chỉ khi banks đã tải xong và khi trang thay đổi
  useEffect(() => {
    // Chỉ tải orders khi banks đã sẵn sàng
    if (!banksLoaded) return;

    const fetchOrders = async () => {
      try {
        setLoading(true);
        const response = await orderService.getAllOrders(currentPage, itemsPerPage);
        console.log('API Response:', response);

        // Kiểm tra cấu trúc dữ liệu trả về
        if (!response || !response.data) {
          throw new Error('Dữ liệu trả về không hợp lệ');
        }

        // Chuyển đổi dữ liệu nhận được thành định dạng hiển thị
        const formattedOrders: OrderDisplay[] = response.data.map((order: OrderServiceType) => {
          // Lấy thông tin khách hàng và thẻ từ API
          const customerName = order.customer_name || order.customer?.name || 'Không có tên';
          const customerId = order.customer?.id || 0;
          const cardNumber = order.card_number || order.card?.card_number || 'Không có';

          // Log thông tin đơn hàng để debug
          if (order.id === 151 || order.id === 169 || order.id === 184) {
            console.log(`Order ${order.id} details:`, {
              customer_name: order.customer_name,
              customer: order.customer,
              card: order.card,
              order_type: order.order_type
            });
          }

          const orderDisplay = {
            id: order.id,
            createdAt: formatDateTimeVN(order.created_at),
            status: order.status,
            customerName: customerName,
            customerId: customerId, // Thêm trường customerId để lọc chính xác hơn
            total_amount: order.total_amount || 0,
            items_count: order.items?.length || 0,
            type: order.order_type as OrderType,
            totalSwiped: order.total_swiped?.toString() || '0',
            totalTransferred: order.total_transferred?.toString() || '0',
            totalFee: order.total_fee || '0',
            cardNumber: cardNumber,
            bank_name: order.bank_name || order.card?.bank_name || 'Không có',
            card_id: order.card?.bank_id
          };

          return orderDisplay;
        });

        setOrders(formattedOrders);
        setFilteredOrders(formattedOrders);

        // Cập nhật thông tin phân trang từ API
        if (response.pagination) {
          // Chỉ cập nhật totalPages, không cập nhật currentPage để tránh vòng lặp vô tận
          setTotalPages(response.pagination.totalPages || 1);
        }
        setError(null);
      } catch (error) {
        console.error('Error fetching orders:', error);
        setError(error instanceof Error ? error.message : 'Có lỗi xảy ra');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [banksLoaded, currentPage]);

  // Hàm tải lại dữ liệu với bộ lọc sử dụng giá trị trực tiếp từ tham số
  const fetchOrdersWithFiltersDirectly = async (filterParams: {
    customerName: string;
    customerId?: number;
    orderType: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => {
    try {
      setLoading(true);

      // Tạo đối tượng filters
      const filters: any = {};

      if (statusFilter) {
        filters.status = statusFilter;
      }

      if (filterParams.orderType && filterParams.orderType !== "all") {
        filters.order_type = filterParams.orderType;
      }

      if (filterParams.customerName && filterParams.customerName !== "all") {
        filters.customer_name = filterParams.customerName;
      }

      // Sử dụng customer_id nếu có (chính xác hơn)
      // Kiểm tra rõ ràng customerId có phải là undefined hoặc null không
      if (filterParams.customerId !== undefined && filterParams.customerId !== null) {
        filters.customer_id = filterParams.customerId;
        // Khi sử dụng customer_id, không sử dụng customer_name để tránh xung đột
        delete filters.customer_name;
      }

      if (filterParams.startDate) {
        filters.start_date = filterParams.startDate.toISOString().split('T')[0];
      }

      if (filterParams.endDate) {
        filters.end_date = filterParams.endDate.toISOString().split('T')[0];
      }

      if (debouncedSearchTerm) {
        filters.search = debouncedSearchTerm;
      }

      // Gọi API với các tham số lọc
      const response = await orderService.getAllOrders(currentPage, itemsPerPage, filters);

      // Xử lý dữ liệu trả về
      if (!response || !response.data) {
        throw new Error('Dữ liệu trả về không hợp lệ');
      }

      // Chuyển đổi dữ liệu nhận được thành định dạng hiển thị
      const formattedOrders: OrderDisplay[] = response.data.map((order: OrderServiceType) => {
        // Lấy thông tin khách hàng và thẻ từ API
        const customerName = order.customer_name || order.customer?.name || 'Không có tên';
        const customerId = order.customer?.id || 0;
        const cardNumber = order.card_number || order.card?.card_number || 'Không có';

        return {
          id: order.id,
          createdAt: formatDateTimeVN(order.created_at),
          status: order.status,
          customerName: customerName,
          customerId: customerId,
          total_amount: order.total_amount || 0,
          items_count: order.items?.length || 0,
          type: order.order_type as OrderType,
          totalSwiped: order.total_swiped?.toString() || '0',
          totalTransferred: order.total_transferred?.toString() || '0',
          totalFee: order.total_fee || '0',
          cardNumber: cardNumber,
          bank_name: order.bank_name || order.card?.bank_name || 'Không có',
          card_id: order.card?.bank_id
        };
      });

      setOrders(formattedOrders);
      setFilteredOrders(formattedOrders);

      // Cập nhật thông tin phân trang từ API
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages || 1);
      }

      // Lấy thống kê đơn hàng
      fetchOrderStats();

      setError(null);
    } catch (error) {
      console.error('Error fetching orders with filters:', error);
      setError(error instanceof Error ? error.message : 'Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };

  // Hàm tải lại dữ liệu với bộ lọc sử dụng state
  const fetchOrdersWithFilters = async () => {
    try {
      setLoading(true);

      // Tạo đối tượng filters
      const filters: any = {};

      if (statusFilter) {
        filters.status = statusFilter;
      }

      if (filterOrderType && filterOrderType !== "all") {
        filters.order_type = filterOrderType;
      }

      if (filterCustomerName && filterCustomerName !== "all") {
        filters.customer_name = filterCustomerName;
      }

      // Sử dụng customer_id nếu có (chính xác hơn)
      // Kiểm tra rõ ràng filterCustomerId có phải là undefined hoặc null không
      if (filterCustomerId !== undefined && filterCustomerId !== null) {
        filters.customer_id = filterCustomerId;
        // Khi sử dụng customer_id, không sử dụng customer_name để tránh xung đột
        delete filters.customer_name;
      }

      if (filterStartDate) {
        filters.start_date = filterStartDate.toISOString().split('T')[0];
      }

      if (filterEndDate) {
        filters.end_date = filterEndDate.toISOString().split('T')[0];
      }

      if (debouncedSearchTerm) {
        filters.search = debouncedSearchTerm;
      }

      // Gọi API với các tham số lọc
      const response = await orderService.getAllOrders(currentPage, itemsPerPage, filters);

      // Xử lý dữ liệu trả về
      if (!response || !response.data) {
        throw new Error('Dữ liệu trả về không hợp lệ');
      }

      // Chuyển đổi dữ liệu nhận được thành định dạng hiển thị
      const formattedOrders: OrderDisplay[] = response.data.map((order: OrderServiceType) => {
        // Lấy thông tin khách hàng và thẻ từ API
        const customerName = order.customer_name || order.customer?.name || 'Không có tên';
        const customerId = order.customer?.id || 0;
        const cardNumber = order.card_number || order.card?.card_number || 'Không có';

        return {
          id: order.id,
          createdAt: formatDateTimeVN(order.created_at),
          status: order.status,
          customerName: customerName,
          customerId: customerId,
          total_amount: order.total_amount || 0,
          items_count: order.items?.length || 0,
          type: order.order_type as OrderType,
          totalSwiped: order.total_swiped?.toString() || '0',
          totalTransferred: order.total_transferred?.toString() || '0',
          totalFee: order.total_fee || '0',
          cardNumber: cardNumber,
          bank_name: order.bank_name || order.card?.bank_name || 'Không có',
          card_id: order.card?.bank_id
        };
      });

      setOrders(formattedOrders);
      setFilteredOrders(formattedOrders);

      // Cập nhật thông tin phân trang từ API
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages || 1);
      }

      // Lấy thống kê đơn hàng
      fetchOrderStats();

      setError(null);
    } catch (error) {
      console.error('Error fetching orders with filters:', error);
      setError(error instanceof Error ? error.message : 'Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý debounce cho tìm kiếm
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setPrevSearchTerm(debouncedSearchTerm); // Lưu giá trị tìm kiếm trước đó
    }, 500); // 500ms debounce

    return () => {
      clearTimeout(timer);
    };
  }, [searchTerm, debouncedSearchTerm]);

  // Cập nhật useEffect để áp dụng bộ lọc
  useEffect(() => {
    if (!banksLoaded) return;

    // Kiểm tra xem người dùng đã xóa từ khóa tìm kiếm hay chưa
    const searchTermCleared = prevSearchTerm && !debouncedSearchTerm;

    // Nếu không có bộ lọc nào được áp dụng, sử dụng dữ liệu hiện tại
    if (!debouncedSearchTerm && !statusFilter && filterCustomerName === "all" && !filterCustomerId && filterOrderType === "all" && !filterStartDate && !filterEndDate) {
      // Tải lại dữ liệu khi trang thay đổi hoặc khi người dùng xóa từ khóa tìm kiếm
      if (orders.length === 0 || searchTermCleared || currentPage > 1) {
        fetchOrdersWithFilters();
      }
      return;
    }

    // Tải lại dữ liệu với bộ lọc
    fetchOrdersWithFilters();
  }, [banksLoaded, currentPage, statusFilter, filterOrderType, filterCustomerName, filterCustomerId, filterStartDate, filterEndDate, debouncedSearchTerm, prevSearchTerm, orders.length]);

  // Xử lý thay đổi chế độ xem
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    if (mode === 'list') {
      setSelectedOrderId(null);
    }
  };

  // Không cần hàm handleCreateOrder nữa vì đã được xử lý trong CreateOrderButton

  // Xử lý navigation
  useEffect(() => {
    // Chỉ áp dụng khi trang được tải lần đầu, không áp dụng khi đã chọn một đơn hàng từ danh sách
    const orderId = params.id;
    if (orderId && viewMode === 'list') {
      setViewMode("detail");
      setSelectedOrderId(Number(orderId));
    } else if (!orderId && !selectedOrderId) {
      setViewMode("list");
      setSelectedOrderId(null);
    }
  }, [params.id, viewMode, selectedOrderId]);

  // Xử lý khi click vào đơn hàng
  const handleOrderClick = (orderId: number) => {
    setViewMode("detail");
    setSelectedOrderId(orderId);
  };

  // Handle viewing order details
  const handleViewDetail = (orderId: number) => {
    setViewMode("detail");
    setSelectedOrderId(orderId);
  };

  // Handle editing an order
  const handleEditOrder = (orderId: number) => {
    setEditOrderId(orderId);
    setShowEditModal(true);
  };

  // Đã có hàm handleEditSuccess ở dưới nên không cần khai báo lại

  // Xử lý sau khi tạo đơn hàng thành công
  // Hàm callback khi tạo đơn hàng mới thành công
  const handleOrderCreateSuccess = async () => {
    // Tải lại danh sách đơn hàng
    await fetchOrdersWithFilters();
    // Cập nhật thống kê
    fetchOrderStats();
  };

  // Hàm để xử lý việc click vào thống kê trạng thái
  const handleStatusFilterClick = async (status: string) => {
    // Nếu nhấp vào cùng một trạng thái, bỏ bộ lọc
    if (statusFilter === status) {
      // Tải lại dữ liệu khi bỏ lọc
      try {
        setLoading(true);
        const response = await orderService.getAllOrders(1, itemsPerPage);

        if (response && response.data) {
          // Chuyển đổi dữ liệu nhận được thành định dạng hiển thị
          const formattedOrders: OrderDisplay[] = response.data.map((order: OrderServiceType) => {
            const customerName = order.customer_name || order.customer?.name || 'Không có tên';
            const customerId = order.customer?.id || 0;
            const cardNumber = order.card_number || order.card?.card_number || 'Không có';

            return {
              id: order.id,
              createdAt: formatDateTimeVN(order.created_at),
              status: order.status,
              customerName: customerName,
              customerId: customerId,
              total_amount: order.total_amount || 0,
              items_count: order.items?.length || 0,
              type: order.order_type as OrderType,
              totalSwiped: order.total_swiped?.toString() || '0',
              totalTransferred: order.total_transferred?.toString() || '0',
              totalFee: order.total_fee || '0',
              cardNumber: cardNumber,
              bank_name: order.bank_name || order.card?.bank_name || 'Không có',
              card_id: order.card?.bank_id
            };
          });

          setOrders(formattedOrders);
          setFilteredOrders(formattedOrders);

          // Cập nhật thông tin phân trang từ API
          if (response.pagination) {
            setTotalPages(response.pagination.totalPages || 1);
          }
        }
      } catch (error) {
        console.error('Error fetching orders:', error);
        setError(error instanceof Error ? error.message : 'Có lỗi xảy ra');
      } finally {
        setLoading(false);
      }

      // Đặt lại bộ lọc sau khi đã tải dữ liệu
      setStatusFilter(null);
    } else {
      setStatusFilter(status);
    }

    // Reset về trang đầu tiên khi thay đổi bộ lọc
    setCurrentPage(1);
  };

  // Cập nhật hàm handleBackToList để reset bộ lọc và tải lại dữ liệu
  const handleBackToList = async () => {
    // Tải lại dữ liệu trước khi quay lại danh sách
    await fetchOrdersWithFilters();
    // Cập nhật thống kê
    fetchOrderStats();

    // Chuyển về chế độ danh sách
    setViewMode("list");
    setSelectedOrderId(null);
    setStatusFilter(null);
  };

  // Lấy lại danh sách đơn hàng
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await orderService.getAllOrders(currentPage, itemsPerPage);
      console.log('API Response:', response);

      // Chuyển đổi dữ liệu nhận được thành định dạng hiển thị
      const formattedOrders: OrderDisplay[] = response.data.map((order: OrderServiceType) => {
        const orderDisplay = {
          id: order.id,
          createdAt: formatDateTimeVN(order.created_at),
          status: order.status,
          customerName: order.customer_name || order.customer?.name || 'Không có tên',
          total_amount: order.total_amount || 0,
          items_count: order.items?.length || 0,
          type: order.order_type as OrderType,
          totalSwiped: order.total_swiped?.toString() || '0',
          totalTransferred: order.total_transferred?.toString() || '0',
          totalFee: order.total_fee || '0',
          cardNumber: order.card_number || order.card?.card_number || 'Không có',
          bank_name: order.bank_name || order.card?.bank_name || 'Không có',
          card_id: order.card?.bank_id
        };

        return orderDisplay;
      });

      setOrders(formattedOrders);
      setFilteredOrders(formattedOrders);
      setError(null);
    } catch (error) {
      console.error('Error fetching orders:', error);
      setError(error instanceof Error ? error.message : 'Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };



  // Handle deleting an order
  const handleDeleteOrder = async (order: OrderDisplay) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')) {
      try {
        await orderService.deleteOrder(order.id);
        toast.success('Đã xóa đơn hàng thành công');

        // Cập nhật lại danh sách đơn hàng
        setOrders(orders.filter(ord => ord.id !== order.id));
        setFilteredOrders(filteredOrders.filter(ord => ord.id !== order.id));
      } catch (err) {
        console.error('Lỗi khi xóa đơn hàng:', err);
        toast.error('Không thể xóa đơn hàng. Vui lòng thử lại sau.');
      }
    }
  };



  // Hàm lấy tên ngân hàng từ bank_id
  const getBankNameById = (bankId: number | undefined): string => {
    if (!bankId) return "Không xác định";
    return getBankName(bankId, banks);
  };

  // Sử dụng trực tiếp filteredOrders thay vì cắt lại, vì dữ liệu đã được phân trang từ API
  const paginatedOrders = useMemo(() => {
    // Nếu đang áp dụng bộ lọc, thực hiện phân trang ở client-side
    if (searchTerm || statusFilter || filterCustomerName !== "all" || filterOrderType !== "all" || filterStartDate || filterEndDate) {
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      return filteredOrders.slice(startIndex, endIndex);
    }
    // Nếu không có bộ lọc, sử dụng trực tiếp dữ liệu từ API đã được phân trang
    return filteredOrders;
  }, [filteredOrders, currentPage, itemsPerPage, searchTerm, statusFilter, filterCustomerName, filterOrderType, filterStartDate, filterEndDate]);

  // Tổng số trang
  const [totalPages, setTotalPages] = useState(1);

  // Phân trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Thêm nút phân trang vào cuối danh sách
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const maxPagesToShow = 5;
    const startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    const adjustedStartPage = Math.max(1, endPage - maxPagesToShow + 1);

    return (
      <div className="flex items-center justify-center space-x-2 mt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          Trước
        </Button>

        {adjustedStartPage > 1 && (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(1)}
            >
              1
            </Button>
            {adjustedStartPage > 2 && (
              <span className="px-2 text-muted-foreground">...</span>
            )}
          </>
        )}

        {Array.from({ length: endPage - adjustedStartPage + 1 }, (_, i) => adjustedStartPage + i).map(page => (
          <Button
            key={page}
            variant={currentPage === page ? "default" : "outline"}
            size="sm"
            onClick={() => handlePageChange(page)}
          >
            {page}
          </Button>
        ))}

        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && (
              <span className="px-2 text-muted-foreground">...</span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(totalPages)}
            >
              {totalPages}
            </Button>
          </>
        )}

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Sau
        </Button>
      </div>
    );
  };

  // Lấy số lượng đơn hàng theo trạng thái từ API
  const countOrdersByStatus = (status: string): number => {
    if (status === 'pending') {
      return orderStats.statusCounts.pending;
    } else if (status === 'processing') {
      return orderStats.statusCounts.processing;
    } else if (status === 'completed') {
      return orderStats.statusCounts.completed;
    }
    return 0;
  };

  // Xử lý khi chỉnh sửa đơn hàng thành công
  const handleEditSuccess = async () => {
    // Tải lại danh sách đơn hàng
    fetchOrders();
    // Cập nhật thống kê
    fetchOrderStats();
  };

  // Xử lý khi áp dụng bộ lọc
  const handleApplyFilters = (filters: {
    customerName: string;
    customerId?: number;
    orderType: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => {
    // Đặt lại trang về trang đầu tiên khi áp dụng bộ lọc mới
    setCurrentPage(1);

    // Đặt các giá trị bộ lọc
    setFilterCustomerName(filters.customerName);
    setFilterCustomerId(filters.customerId);
    setFilterOrderType(filters.orderType);
    setFilterStartDate(filters.startDate);
    setFilterEndDate(filters.endDate);

    // Sử dụng giá trị mới nhất từ filters thay vì state
    // vì state có thể chưa được cập nhật do tính chất bất đồng bộ của setState
    fetchOrdersWithFiltersDirectly({
      customerName: filters.customerName,
      customerId: filters.customerId,
      orderType: filters.orderType,
      startDate: filters.startDate,
      endDate: filters.endDate
    });
  };

  // Xử lý khi đặt lại bộ lọc
  const handleResetFilters = () => {
    // Đặt lại tất cả các bộ lọc
    setFilterCustomerName("all");
    setFilterCustomerId(undefined);
    setFilterOrderType("all");
    setFilterStartDate(undefined);
    setFilterEndDate(undefined);
    setStatusFilter(null);
    setSearchTerm("");
    setDebouncedSearchTerm("");
    setPrevSearchTerm("");

    // Đặt lại trang về trang đầu tiên
    setCurrentPage(1);

    // Sử dụng hàm fetchOrdersWithFiltersDirectly để tải lại dữ liệu
    // Sử dụng giá trị trực tiếp thay vì state để tránh vấn đề bất đồng bộ
    fetchOrdersWithFiltersDirectly({
      customerName: "all",
      customerId: undefined,
      orderType: "all",
      startDate: undefined,
      endDate: undefined
    });
  };

  // Render based on the current view mode
  const renderContent = () => {
    switch (viewMode) {
      case "detail":
        return (
          <OrderDetail
            orderId={selectedOrderId || ""}
            onBack={handleBackToList}
            onEdit={handleEditOrder}
            onDataChange={handleEditSuccess}
          />
        );
      case "form":
        return (
          <OrderForm
            orderId={selectedOrderId ? selectedOrderId.toString() : undefined}
            onCancel={handleBackToList}
          />
        );
      case "list":
      default:
        return (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <h2 className="text-xl sm:text-2xl font-bold tracking-tight">Quản lý đơn hàng</h2>
              <CreateOrderButton className="w-full sm:w-auto" onOrderCreated={handleOrderCreateSuccess} />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 md:gap-6">
              <Card
                className={`${statusFilter === 'pending' ? 'ring-2 ring-primary' : ''} hover:shadow-md transition-all duration-200 cursor-pointer`}
                onClick={() => handleStatusFilterClick('pending')}
              >
                <CardHeader className="p-2 pb-1 md:p-4 md:pb-2">
                  <CardTitle className="text-xs md:text-sm font-medium text-muted-foreground">
                    Chờ xử lý
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-2 pt-0 md:p-4 md:pt-0">
                  <div
                    className={`text-lg md:text-2xl font-bold ${statusFilter === 'pending' ? 'text-primary' : ''}`}
                  >
                    {countOrdersByStatus('pending')}
                  </div>
                </CardContent>
              </Card>

              <Card
                className={`${statusFilter === 'processing' ? 'ring-2 ring-primary' : ''} hover:shadow-md transition-all duration-200 cursor-pointer`}
                onClick={() => handleStatusFilterClick('processing')}
              >
                <CardHeader className="p-2 pb-1 md:p-4 md:pb-2">
                  <CardTitle className="text-xs md:text-sm font-medium text-muted-foreground">
                    Đang xử lý
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-2 pt-0 md:p-4 md:pt-0">
                  <div
                    className={`text-lg md:text-2xl font-bold ${statusFilter === 'processing' ? 'text-primary' : ''}`}
                  >
                    {countOrdersByStatus('processing')}
                  </div>
                </CardContent>
              </Card>

              <Card
                className={`${statusFilter === 'completed' ? 'ring-2 ring-primary' : ''} hover:shadow-md transition-all duration-200 cursor-pointer`}
                onClick={() => handleStatusFilterClick('completed')}
              >
                <CardHeader className="p-2 pb-1 md:p-4 md:pb-2">
                  <CardTitle className="text-xs md:text-sm font-medium text-muted-foreground">
                    Hoàn thành
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-2 pt-0 md:p-4 md:pt-0">
                  <div
                    className={`text-lg md:text-2xl font-bold ${statusFilter === 'completed' ? 'text-primary' : ''}`}
                  >
                    {countOrdersByStatus('completed')}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-medium">Danh sách đơn hàng</CardTitle>
                <CardDescription>
                  Quản lý đơn hàng và theo dõi tiến độ giao dịch
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Tìm kiếm theo tên khách hàng hoặc số thẻ..."
                        className="pl-9"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className="shrink-0"
                      onClick={() => setShowFilterModal(true)}
                    >
                      <Filter className="h-4 w-4" />
                      <span className="sr-only">Filter</span>
                    </Button>
                  </div>

                  {(statusFilter || filterCustomerName !== "all" || filterOrderType !== "all" || filterStartDate || filterEndDate) && (
                    <Badge variant="outline" className="py-1.5 px-3 text-xs whitespace-nowrap overflow-hidden text-ellipsis">
                      Đang lọc
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-2 -mr-1"
                        onClick={handleResetFilters}
                      >
                        ✕
                      </Button>
                    </Badge>
                  )}
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Đang tải dữ liệu...</span>
                  </div>
                ) : error ? (
                  <div className="text-center py-8 text-rose-500">{error}</div>
                ) : filteredOrders.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p className="mb-4">Không tìm thấy đơn hàng nào trong hệ thống</p>
                    <CreateOrderButton className="mx-auto" onOrderCreated={handleOrderCreateSuccess} />
                  </div>
                ) : (
                  <>
                    {/* Desktop Table - Hidden on mobile */}
                    <div className="rounded-lg border overflow-hidden hidden md:block">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>ID</TableHead>
                            <TableHead>Ngày tạo</TableHead>
                            <TableHead>Trạng thái</TableHead>
                            <TableHead>Khách hàng</TableHead>
                            <TableHead>Tổng tiền</TableHead>
                            <TableHead>Loại</TableHead>
                            <TableHead>Thao tác</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {loading ? (
                            <TableRow>
                              <TableCell colSpan={7} className="h-24 text-center">
                                <div className="flex justify-center">
                                  <Loader2 className="w-6 h-6 text-gray-400 animate-spin" />
                                </div>
                              </TableCell>
                            </TableRow>
                          ) : paginatedOrders.length > 0 ? (
                            paginatedOrders.map((order) => (
                              <TableRow key={order.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleOrderClick(order.id)}>
                                <TableCell className="font-medium">#{order.id}</TableCell>
                                <TableCell>{order.createdAt}</TableCell>
                                <TableCell>
                                  <Badge className={`${statusStyles[order.status].bgColor} ${statusStyles[order.status].color} ${statusStyles[order.status].borderColor} flex items-center justify-center py-1 px-2`}>
                                    {statusStyles[order.status].label}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex flex-col">
                                    <div>{order.customerName}</div>
                                    <div className="text-xs text-gray-500">
                                      {order.cardNumber}
                                      {order.card_id && (
                                        <span> - {getBankNameById(order.card_id)}</span>
                                      )}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>{new Intl.NumberFormat('vi-VN', {maximumFractionDigits: 0}).format(order.total_amount)}</TableCell>
                                <TableCell>
                                  <Badge className={`${typeStyles[order.type].bgColor} ${typeStyles[order.type].color} ${typeStyles[order.type].borderColor} flex items-center justify-center py-1 px-2`}>
                                    {order.type}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center justify-end space-x-2">
                                    <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); handleEditOrder(order.id); }}>
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteOrder(order);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={7} className="h-24 text-center text-muted-foreground">
                                {searchTerm ? "Không tìm thấy đơn hàng nào phù hợp" : "Chưa có đơn hàng nào"}
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Mobile Card View - Visible only on mobile */}
                    <div className="space-y-2 md:hidden">
                      {loading ? (
                        <div className="flex justify-center items-center py-8">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                          <span className="ml-2">Đang tải dữ liệu...</span>
                        </div>
                      ) : paginatedOrders.length > 0 ? (
                        paginatedOrders.map((order) => (
                          <div
                            key={order.id}
                            className="border rounded-lg p-3 shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 hover:bg-accent/10"
                            onClick={() => handleOrderClick(order.id)}
                          >
                            <div className="flex justify-between items-start mb-1.5">
                              <div className="flex items-center gap-2">
                                <div className="font-medium">#{order.id}</div>
                                <Badge className={`${statusStyles[order.status].bgColor} ${statusStyles[order.status].color} ${statusStyles[order.status].borderColor} flex items-center justify-center py-0.5 px-1.5 text-xs`}>
                                  {statusStyles[order.status].label}
                                </Badge>
                              </div>
                              <div className="flex space-x-1">
                                <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => { e.stopPropagation(); handleEditOrder(order.id); }}>
                                  <Edit className="h-3.5 w-3.5" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-7 w-7"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteOrder(order);
                                  }}
                                >
                                  <Trash2 className="h-3.5 w-3.5" />
                                </Button>
                              </div>
                            </div>

                            <div className="text-xs text-muted-foreground mb-1.5">{order.createdAt}</div>

                            <div className="grid grid-cols-2 gap-3 mb-1.5">
                              <div>
                                <div className="text-xs text-muted-foreground">Khách hàng</div>
                                <div className="text-sm font-medium truncate">{order.customerName}</div>
                              </div>

                              <div>
                                <div className="text-xs text-muted-foreground">Tổng tiền</div>
                                <div className="text-sm font-medium">{new Intl.NumberFormat('vi-VN', {maximumFractionDigits: 0}).format(order.total_amount)}</div>
                              </div>
                            </div>

                            <div className="flex justify-between items-end mb-1.5">
                              <div className="flex-1 mr-2">
                                <div className="text-xs text-muted-foreground">Thẻ</div>
                                <div className="text-sm truncate">
                                  {order.cardNumber}
                                  {order.card_id && (
                                    <span className="text-xs text-muted-foreground"> - {getBankNameById(order.card_id)}</span>
                                  )}
                                </div>
                              </div>

                              <Badge className={`${typeStyles[order.type].bgColor} ${typeStyles[order.type].color} ${typeStyles[order.type].borderColor} flex items-center justify-center py-0.5 px-1.5 text-xs shrink-0`}>
                                {order.type}
                              </Badge>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          {searchTerm ? "Không tìm thấy đơn hàng nào phù hợp" : "Chưa có đơn hàng nào"}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {renderPagination()}
          </div>
        );
    }
  };

  return (
    <Layout>
      {renderContent()}

      {/* Modal chỉnh sửa đơn hàng - luôn render nhưng chỉ hiển thị khi showEditModal=true */}
      <OrderEditModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        orderId={editOrderId || 0}
        onSuccess={handleEditSuccess}
      />

      {/* Không cần OrderCreateModal ở đây nữa vì đã được bao gồm trong CreateOrderButton */}

      <OrderFilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        initialFilters={{
          customerName: filterCustomerName,
          orderType: filterOrderType,
          startDate: filterStartDate,
          endDate: filterEndDate,
        }}
        orders={orders}
      />
    </Layout>
  );
}
