import { useEffect, useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, Trash2, Loader2, Calendar, X, User, Building2, DollarSign, CreditCard } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { MoneyTransfer, getMoneyTransferList, deleteMoneyTransfer } from "@/services/moneyTransferService";
import apiClient from "@/lib/apiClient";
import { MoneyTransferFilterModal } from "@/components/money-transfers/MoneyTransferFilterModal";
import { toast } from "sonner";
import { getBankList, Bank } from "@/services/bankService";
import { formatDateTimeVN } from "@/utils/date-format";

export default function MoneyTransfers() {
  const [searchTerm, setSearchTerm] = useState("");
  const [moneyTransfers, setMoneyTransfers] = useState<MoneyTransfer[]>([]);
  const [filteredTransfers, setFilteredTransfers] = useState<MoneyTransfer[]>([]);
  const [loading, setLoading] = useState(true);

  const [bankList, setBankList] = useState<Bank[]>([]);
  const [loadingBankList, setLoadingBankList] = useState(false);

  // Phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Bộ lọc
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showFilterModal, setShowFilterModal] = useState(false);

  useEffect(() => {
    document.title = "Quản lý chuyển tiền | Tín Phát Credit";
  }, []);

  const fetchTransfers = async (page = currentPage, limit = pageSize) => {
    setLoading(true);
    try {
      // Xây dựng các tham số lọc
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      if (searchTerm) params.append('search', searchTerm);
      if (startDate) {
        // Format ngày thành YYYY-MM-DD
        params.append('start_date', format(startDate, 'yyyy-MM-dd'));
      }
      if (endDate) {
        // Format ngày thành YYYY-MM-DD và thêm 1 ngày để bao gồm cả ngày kết thúc
        const nextDay = new Date(endDate);
        nextDay.setDate(nextDay.getDate() + 1);
        params.append('end_date', format(nextDay, 'yyyy-MM-dd'));
      }

      // Gọi API với phân trang và bộ lọc
      const response = await apiClient.get(`/api/transfers?${params.toString()}`);
      const transferResponse = response.data;

      if (transferResponse.success) {
        setMoneyTransfers(transferResponse.data);
        setFilteredTransfers(transferResponse.data);
        setTotalItems(transferResponse.total || 0);
        setTotalPages(transferResponse.totalPages || 1);
      } else {
        toast.error('Không thể tải dữ liệu giao dịch chuyển tiền');
        setMoneyTransfers([]);
        setFilteredTransfers([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('Error fetching transfers:', error);
      toast.error('Lỗi khi tải dữ liệu giao dịch chuyển tiền');
      setMoneyTransfers([]);
      setFilteredTransfers([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const fetchBanks = async () => {
    setLoadingBankList(true);
    try {
      const bankData = await getBankList();
      if (bankData && bankData.success && Array.isArray(bankData.data)) {
        setBankList(bankData.data);
      } else {
        console.error("Failed to fetch banks:", bankData);
        setBankList([]);
      }
    } catch (error) {
      console.error('Error fetching banks:', error);
      setBankList([]);
    } finally {
      setLoadingBankList(false);
    }
  };

  // Tải dữ liệu ban đầu
  useEffect(() => {
    fetchTransfers();
    fetchBanks();
  }, []);

  // Tải lại khi thay đổi trang hoặc kích thước trang
  useEffect(() => {
    fetchTransfers(currentPage, pageSize);
  }, [currentPage, pageSize]);

  // Xử lý tìm kiếm bằng cách gọi API
  useEffect(() => {
    // Sử dụng debounce để tránh gọi API quá nhiều khi người dùng đang gõ
    const debounceTimeout = setTimeout(() => {
      setCurrentPage(1);
      fetchTransfers(1, pageSize);
    }, 500);

    return () => clearTimeout(debounceTimeout);
  }, [searchTerm]);

  // Hiển thị kết quả tìm kiếm
  useEffect(() => {
    setFilteredTransfers(moneyTransfers);
  }, [moneyTransfers]);

  const handleDeleteTransfer = async (transferId: number) => {
    if (confirm('Bạn có chắc chắn muốn xóa giao dịch chuyển tiền này?')) {
      try {
        await deleteMoneyTransfer(transferId);
        toast.success('Đã xóa giao dịch chuyển tiền thành công');

        // Nếu đang ở trang cuối và chỉ còn 1 mục, quay lại trang trước
        if (filteredTransfers.length === 1 && currentPage > 1) {
          setCurrentPage(prev => prev - 1);
        } else {
          // Nếu không, tải lại trang hiện tại
          fetchTransfers(currentPage, pageSize);
        }
      } catch (error) {
        console.error('Error deleting money transfer:', error);
        toast.error('Không thể xóa giao dịch chuyển tiền');
      }
    }
  };

  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined) return '0';
    const numAmount = Number(amount);
    if (isNaN(numAmount)) return '0';
    return Math.round(numAmount).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return formatDateTimeVN(dateString);
    } catch (e) {
      return 'Lỗi định dạng';
    }
  };

  const getBankName = (bankId: number | undefined | null): string => {
    if (bankId === null || bankId === undefined) return '';
    if (loadingBankList) return '...';
    const bank = bankList.find(b => b.id === bankId);
    return bank ? bank.name : '';
  };

  // Hàm xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Hàm xử lý thay đổi kích thước trang - đã bỏ đi
  // const handlePageSizeChange = (size: string) => {
  //   const newSize = parseInt(size);
  //   setPageSize(newSize);
  //   setCurrentPage(1); // Reset về trang 1 khi thay đổi kích thước trang
  // };

  // Hàm đặt lại bộ lọc
  const handleResetFilters = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    setSearchTerm("");
    setCurrentPage(1);
    fetchTransfers(1, pageSize);
  };

  // Hàm áp dụng bộ lọc
  const handleApplyFilters = (filters?: {
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => {
    if (filters) {
      setStartDate(filters.startDate);
      setEndDate(filters.endDate);

      // Đặt timeout ngắn để đảm bảo các state được cập nhật trước khi gọi fetchTransfers
      setTimeout(() => {
        setCurrentPage(1);
        fetchTransfers(1, pageSize);
      }, 0);
    } else {
      setCurrentPage(1);
      fetchTransfers(1, pageSize);
    }
  };

  // Hàm hiển thị phân trang với responsive design
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    // Show fewer pages on mobile
    const isMobile = window.innerWidth < 768;
    const maxPagesToShow = isMobile ? 3 : 5;
    const startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    const adjustedStartPage = Math.max(1, endPage - maxPagesToShow + 1);

    return (
      <div className="mt-6">
        {/* Mobile Pagination - Simple */}
        <div className="flex md:hidden items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center gap-1"
          >
            <span>Trước</span>
          </Button>

          <span className="text-sm text-muted-foreground">
            Trang {currentPage} / {totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center gap-1"
          >
            <span>Sau</span>
          </Button>
        </div>

        {/* Desktop Pagination - Full */}
        <Pagination className="hidden md:flex">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => handlePageChange(currentPage - 1)}
                className={currentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                aria-disabled={currentPage === 1}
                aria-label="Trang trước"
              />
            </PaginationItem>

            {adjustedStartPage > 1 && (
              <>
                <PaginationItem>
                  <PaginationLink onClick={() => handlePageChange(1)} className="cursor-pointer">
                    1
                  </PaginationLink>
                </PaginationItem>
                {adjustedStartPage > 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
              </>
            )}

            {Array.from({ length: endPage - adjustedStartPage + 1 }, (_, i) => adjustedStartPage + i).map(page => (
              <PaginationItem key={page}>
                <PaginationLink
                  onClick={() => handlePageChange(page)}
                  isActive={currentPage === page}
                  className={currentPage !== page ? "cursor-pointer" : ""}
                  aria-current={currentPage === page ? "page" : undefined}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}

            {endPage < totalPages && (
              <>
                {endPage < totalPages - 1 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                <PaginationItem>
                  <PaginationLink onClick={() => handlePageChange(totalPages)} className="cursor-pointer">
                    {totalPages}
                  </PaginationLink>
                </PaginationItem>
              </>
            )}

            <PaginationItem>
              <PaginationNext
                onClick={() => handlePageChange(currentPage + 1)}
                className={currentPage === totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                aria-disabled={currentPage === totalPages}
                aria-label="Trang kế tiếp"
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    );
  };

  // Mobile Card View Component
  const MobileCardView = ({ transfer }: { transfer: MoneyTransfer }) => (
    <Card className="mb-3 shadow-sm">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs font-medium">
              CT#{transfer.id}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              #{transfer.order_id}
            </Badge>
          </div>
          <Button
            size="icon"
            variant="ghost"
            onClick={() => handleDeleteTransfer(transfer.id!)}
            className="text-destructive hover:text-destructive hover:bg-destructive/10 h-8 w-8 flex-shrink-0"
            aria-label="Xóa"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-3">
          {/* Customer and Card Info */}
          <div className="flex items-start gap-3">
            <User className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <p className="font-medium text-sm leading-tight">
                {transfer.order?.Customer?.name || 'Không rõ'}
              </p>
              <p className="text-xs text-muted-foreground mt-1 leading-tight">
                {transfer.order?.Card?.card_number || 'Không có thẻ'}
                {transfer.order?.Card?.bank_id && (
                  <span className="inline-block ml-1 px-1.5 py-0.5 bg-primary/10 text-primary rounded text-xs">
                    {getBankName(transfer.order.Card.bank_id)}
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Date and Amount */}
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-xs text-muted-foreground">
                {formatDate(transfer.transfer_date)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-semibold text-sm text-emerald-600">
                {formatCurrency(transfer.amount)}
              </span>
            </div>
          </div>

          {/* Note */}
          {transfer.note && (
            <div className="pt-2 border-t">
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">Ghi chú:</span> {transfer.note}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderListContent = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div>
                <CardTitle className="text-lg font-medium">Giao dịch chuyển tiền</CardTitle>
                <CardDescription className="mt-1">
                  Quản lý các giao dịch chuyển tiền
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {totalItems} giao dịch
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 mb-6">
              {/* Search and Filter Controls */}
              <div className="flex flex-col gap-3">
                {/* Search Bar and Filter Button */}
                <div className="flex gap-3">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm theo mã, khách hàng, số thẻ..."
                      className="pl-9 h-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilterModal(true)}
                    className="flex items-center gap-2 px-3 h-10 flex-shrink-0"
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">Bộ lọc</span>
                  </Button>
                </div>

                {/* Filter Status */}
                {(startDate || endDate) && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="py-1.5 px-3 text-xs">
                      Đang lọc
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-2 -mr-1"
                        onClick={handleResetFilters}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Đang tải dữ liệu...</span>
              </div>
            )}

            {/* Desktop Table View - Hidden on mobile */}
            {!loading && (
              <div className="hidden lg:block">
                <div className="rounded-lg border overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[80px]">Mã CT</TableHead>
                          <TableHead>Thời gian</TableHead>
                          <TableHead>Mã đơn</TableHead>
                          <TableHead>Khách hàng / Thẻ</TableHead>
                          <TableHead>Số tiền</TableHead>
                          <TableHead>Ghi chú</TableHead>
                          <TableHead className="text-right w-[80px]">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredTransfers.map((transfer) => (
                          <TableRow key={transfer.id} className="group hover:bg-muted/50 transition-colors">
                            <TableCell className="font-medium">{transfer.id}</TableCell>
                            <TableCell>{formatDate(transfer.transfer_date)}</TableCell>
                            <TableCell>
                              <Badge variant="outline">#{transfer.order_id}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col">
                                <span>{transfer.order?.Customer?.name || 'Không rõ'}</span>
                                <span className="text-xs text-muted-foreground">
                                  {transfer.order?.Card?.card_number || 'Không có thẻ'}
                                  {transfer.order?.Card?.bank_id &&
                                    ` - ${getBankName(transfer.order.Card.bank_id)}`
                                  }
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="font-medium text-emerald-600">{formatCurrency(transfer.amount)}</TableCell>
                            <TableCell>{transfer.note || "—"}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => handleDeleteTransfer(transfer.id!)}
                                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                  aria-label="Xóa"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}

                        {filteredTransfers.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                              {searchTerm ? 'Không tìm thấy giao dịch nào phù hợp.' : 'Không có giao dịch chuyển tiền nào.'}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}

            {/* Mobile Card View - Visible on mobile and tablet */}
            {!loading && (
              <div className="lg:hidden">
                {filteredTransfers.length > 0 ? (
                  <div className="space-y-4">
                    {filteredTransfers.map((transfer) => (
                      <MobileCardView key={transfer.id} transfer={transfer} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      {searchTerm ? 'Không tìm thấy giao dịch nào phù hợp.' : 'Không có giao dịch chuyển tiền nào.'}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && renderPagination()}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <Layout>
      {renderListContent()}

      {/* Modal bộ lọc */}
      <MoneyTransferFilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        initialFilters={{
          startDate,
          endDate
        }}
      />
    </Layout>
  );
}
