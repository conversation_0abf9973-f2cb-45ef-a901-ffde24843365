import { useEffect, useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  CreditCard,
  Save,
  X,
  Banknote,
  Loader2,
  CheckCircle,
  AlertCircle,
  CalendarIcon,
  RefreshCw,
  Building2,
  DollarSign,
  User
} from "lucide-react";
import { toast } from "sonner";
import { clearPosCache } from "@/services/cacheService";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { POS, getPOSList, createPOS, updatePOS, deletePOS } from "@/services/posService";
import { Bank, getBankList } from "@/services/bankService";
import apiClient from "@/lib/apiClient";
import { formatDateTimeVN } from "@/utils/date-format";

interface POSFormData {
  id?: number;
  name: string;
  visa_fee: number;
  master_fee: number;
  jcb_fee: number;
  napas_fee: number;
  fee_bank?: number;
  daily_limit?: number;
  note?: string;
  balance?: number;
  bank_id?: number;
  status?: string;
  user_id?: number;
}

export default function POSPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [posList, setPosList] = useState<POS[]>([]);
  const [filteredPOS, setFilteredPOS] = useState<POS[]>([]);
  const [showDialog, setShowDialog] = useState(false);
  const [selectedPOS, setSelectedPOS] = useState<POSFormData>({
    name: "",
    visa_fee: 1.3,
    master_fee: 1.3,
    jcb_fee: 1.3,
    napas_fee: 1.0,
    fee_bank: 0.5,
    daily_limit: *********,
    note: "",
    status: "active"
  });
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [banks, setBanks] = useState<Bank[]>([]);
  const [showAddBankDialog, setShowAddBankDialog] = useState(false);
  const [newBankName, setNewBankName] = useState("");
  const [addingBank, setAddingBank] = useState(false);


  // Add page title
  useEffect(() => {
    document.title = "Quản lý POS | Tín Phát Credit";
  }, []);



  // Fetch POS data and banks on mount
  useEffect(() => {
    fetchPOSData();
    fetchBanks();
  }, []);



  // Fetch POS data from API
  const fetchPOSData = async () => {
    setIsLoading(true);
    try {
      // Xóa cache trước khi tải dữ liệu mới
      try {
        await clearPosCache();
      } catch (cacheError) {
        console.error("Error clearing POS cache:", cacheError);
        // Tiếp tục tải dữ liệu ngay cả khi xóa cache thất bại
      }

      const response = await getPOSList();
      if (response.success) {
        setPosList(response.data);
        setFilteredPOS(response.data);

      } else {
        toast.error("Không thể tải dữ liệu POS");
      }
    } catch (error) {
      console.error("Error fetching POS data:", error);
      toast.error("Không thể tải dữ liệu POS");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch banks data
  const fetchBanks = async () => {
    try {
      const response = await getBankList();
      if (response.success) {
        setBanks(response.data);
      } else {
        toast.error("Không thể tải dữ liệu ngân hàng");
      }
    } catch (error) {
      console.error("Error fetching banks data:", error);
      toast.error("Không thể tải dữ liệu ngân hàng");
    }
  };

  // Filter POS based on search term and status
  useEffect(() => {
    let filtered = posList;

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(pos => pos.status === statusFilter);
    }

    // Apply search filter
    if (searchTerm) {
      const lowercaseSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        pos =>
          pos.id.toString().toLowerCase().includes(lowercaseSearch) ||
          pos.name.toLowerCase().includes(lowercaseSearch) ||
          (pos.Bank?.name && pos.Bank.name.toLowerCase().includes(lowercaseSearch))
      );
    }

    setFilteredPOS(filtered);
  }, [searchTerm, statusFilter, posList]);

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', { maximumFractionDigits: 0 }).format(amount);
  };

  // Format percentage for display
  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined) return "0.00";
    // Lấy 2 chữ số thập phân từ giá trị gốc (không làm tròn)
    const truncated = Math.floor(value * 100) / 100;
    return truncated.toFixed(2);
  };

  // Format date for display
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Handle creating a new POS
  const handleCreatePOS = () => {
    setSelectedPOS({
      name: "",
      visa_fee: 1.3,
      master_fee: 1.3,
      jcb_fee: 1.3,
      napas_fee: 1.0,
      fee_bank: 0.5,
      daily_limit: *********,
      note: "",
      status: "active"
    });
    setEditMode(false);
    setShowDialog(true);
  };

  // Handle editing a POS
  const handleEditPOS = (pos: POS) => {
    setSelectedPOS({
      id: pos.id,
      name: pos.name,
      visa_fee: pos.visa_fee,
      master_fee: pos.master_fee,
      jcb_fee: pos.jcb_fee,
      napas_fee: pos.napas_fee,
      fee_bank: pos.fee_bank,
      daily_limit: pos.daily_limit,
      note: pos.note || "",
      balance: pos.balance,
      bank_id: pos.bank_id,
      status: pos.status || "active"
    });
    setEditMode(true);
    setShowDialog(true);
  };

  // Handle input change for form fields
  const handleInputChange = (field: string, value: string | number | boolean) => {
    if (field === 'visa_fee' || field === 'master_fee' || field === 'jcb_fee' || field === 'napas_fee' || field === 'fee_bank') {
      // Allow decimal point in percentage fields
      if (typeof value === 'string') {
        // Replace commas with periods for consistent decimal handling
        let cleanedValue = value.replace(',', '.');

        // Kiểm tra có phải là định dạng số thập phân hợp lệ không - chấp nhận cả chuỗi đang nhập dở (có thể chỉ có dấu chấm)
        // Cho phép: "1", "1.", "1.3", "1.30", ""
        if (/^(\d*\.?\d*)$/.test(cleanedValue)) {
          // Nếu đang nhập "." thì ghi nhận là "0." để dễ nhập tiếp
          if (cleanedValue === '.') {
            cleanedValue = '0.';
          }

          // Lưu giá trị nguyên dạng chuỗi để giữ nguyên dấu chấm khi đang nhập
          setSelectedPOS(prev => ({
            ...prev,
            [field]: cleanedValue === '' ? 0 : cleanedValue
          }));
        }
      } else {
        setSelectedPOS(prev => ({
          ...prev,
          [field]: value
        }));
      }
    } else if (field === 'daily_limit') {
      // Handle as string first to remove formatting
      const stringValue = typeof value === 'string' ? value : value.toString();
      // Remove non-numeric characters
      const numericValue = stringValue.replace(/\D/g, '');
      setSelectedPOS(prev => ({
        ...prev,
        [field]: parseInt(numericValue) || 0
      }));
    } else if (field === 'status') {
      setSelectedPOS(prev => ({
        ...prev,
        [field]: value ? 'active' : 'locked'
      }));
    } else {
      setSelectedPOS(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validation
    if (!selectedPOS.name) {
      toast.error("Vui lòng nhập tên POS");
      return;
    }

    setLoading(true);

    try {
      let response;

      // Chuyển đổi các giá trị phí từ chuỗi sang số
      const posDataToSubmit = {
        ...selectedPOS,
        visa_fee: typeof selectedPOS.visa_fee === 'string' ? parseFloat(selectedPOS.visa_fee) : selectedPOS.visa_fee,
        master_fee: typeof selectedPOS.master_fee === 'string' ? parseFloat(selectedPOS.master_fee) : selectedPOS.master_fee,
        jcb_fee: typeof selectedPOS.jcb_fee === 'string' ? parseFloat(selectedPOS.jcb_fee) : selectedPOS.jcb_fee,
        napas_fee: typeof selectedPOS.napas_fee === 'string' ? parseFloat(selectedPOS.napas_fee) : selectedPOS.napas_fee,
        fee_bank: typeof selectedPOS.fee_bank === 'string' ? parseFloat(selectedPOS.fee_bank) : selectedPOS.fee_bank,
      };

      if (editMode && selectedPOS.id) {
        // Update existing POS
        response = await updatePOS(selectedPOS.id, posDataToSubmit);
      } else {
        // Create new POS
        response = await createPOS({
          ...posDataToSubmit,
          balance: 0 // Ensure balance is set for new POS
        });
      }

      if (response.success) {
        toast.success(
          editMode
            ? "Cập nhật POS thành công"
            : "Tạo POS mới thành công"
        );
        setShowDialog(false);
        fetchPOSData();
      } else {
        toast.error(response.message || "Có lỗi xảy ra");
      }
    } catch (error) {
      console.error("Error saving POS:", error);
      toast.error("Không thể lưu thông tin POS");
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a POS
  const handleDelete = async (id: number) => {
    if (confirm("Bạn có chắc chắn muốn xóa POS này?")) {
      try {
        const response = await deletePOS(id);
        if (response.success) {
          toast.success("Xóa POS thành công");
          fetchPOSData();
        } else {
          toast.error(response.message || "Không thể xóa POS");
        }
      } catch (error) {
        console.error("Error deleting POS:", error);
        toast.error("Không thể xóa POS");
      }
    }
  };

  // Calculate total balance
  const getTotalBalance = () => {
    return posList.reduce((total, pos) => {
      // Ensure balance is a number and not NaN
      const posBalance = pos.balance !== undefined && pos.balance !== null ?
        Number(pos.balance) : 0;
      return total + posBalance;
    }, 0);
  };

  // Get bank name by ID
  const getBankName = (bankId: number | undefined) => {
    if (!bankId) return "Không có";
    const bank = banks.find(b => b.id === bankId);
    return bank ? bank.name : "Không xác định";
  };

  // Add handleAddBank function
  const handleAddBank = async () => {
    if (!newBankName.trim()) {
      toast.error("Vui lòng nhập tên ngân hàng");
      return;
    }

    setAddingBank(true);

    try {
      const response = await apiClient.post('/api/banks', { name: newBankName });
      const data = response.data;

      if (data.success) {
        toast.success("Thêm ngân hàng thành công");
        setNewBankName("");
        setShowAddBankDialog(false);
        // Refresh banks list
        fetchBanks();
      } else {
        toast.error(data.message || "Không thể thêm ngân hàng");
      }
    } catch (error) {
      console.error("Error adding bank:", error);
      toast.error("Không thể thêm ngân hàng");
    } finally {
      setAddingBank(false);
    }
  };

  // Mobile Card View Component
  const MobileCardView = ({ pos }: { pos: POS }) => {
    const balance = pos.balance || 0;

    const getBalanceColor = (balance: number) => {
      if (balance > 0) return "text-rose-500";
      if (balance < 0) return "text-emerald-600";
      return "text-muted-foreground";
    };

    return (
      <Card className="mb-2 shadow-sm cursor-pointer" onClick={() => handleEditPOS(pos)}>
        <CardContent className="p-3">
          {/* Header: POS Name + ID + Actions */}
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
              <span className="font-semibold text-sm truncate flex-shrink-0 min-w-0">{pos.name}</span>
              <Badge variant="outline" className="text-xs font-medium flex-shrink-0">
                #{pos.id}
              </Badge>
              {pos.Bank?.name && (
                <Badge variant="secondary" className="text-xs flex-shrink-0">
                  {pos.Bank.name}
                </Badge>
              )}
            </div>
            <div className="flex gap-1 ml-2 flex-shrink-0">
              <Button
                size="icon"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditPOS(pos);
                }}
                className="h-7 w-7"
                aria-label="Sửa"
              >
                <Edit className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Info Row: Status + Balance */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2">
              {pos.status === 'active' ? (
                <Badge variant="outline" className="bg-green-100 text-green-800 text-xs px-1.5 py-0.5">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Hoạt động
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-red-100 text-red-800 text-xs px-1.5 py-0.5">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Khóa
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span className={`font-medium ${getBalanceColor(balance)}`}>
                {formatCurrency(balance)}
              </span>
            </div>
          </div>

          {/* Fees Row */}
          <div className="flex items-center justify-between text-xs mt-2 pt-2 border-t">
            <div className="flex items-center gap-3">
              <span className="text-muted-foreground">
                Visa: {formatPercentage(pos.visa_fee)}%
              </span>
              <span className="text-muted-foreground">
                Master: {formatPercentage(pos.master_fee)}%
              </span>
            </div>
            <span className="text-muted-foreground">
              Bank: {formatPercentage(pos.fee_bank)}%
            </span>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-2xl font-bold tracking-tight">Quản lý POS</h2>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchPOSData}>
              <RefreshCw className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Làm mới</span>
            </Button>
            <Button className="neo-button" onClick={handleCreatePOS}>
              <Plus className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Thêm POS mới</span>
              <span className="sm:hidden">Thêm</span>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Tổng số POS
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="text-2xl font-bold">{posList.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Tổng tiền đang tồn
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="text-2xl font-bold text-emerald-600">{formatCurrency(getTotalBalance())}</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div>
                <CardTitle className="text-lg font-medium">Danh sách POS</CardTitle>
                <CardDescription className="mt-1">
                  Quản lý thông tin POS và phí
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {filteredPOS.length} POS
                </Badge>
              </div>
            </div>

            <div className="flex flex-col gap-4 mt-4">
              {/* Search and Filter Controls */}
              <div className="flex flex-col gap-3">
                {/* Search Bar and Filter */}
                <div className="flex gap-3">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm POS..."
                      className="pl-9 h-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <Select
                    value={statusFilter}
                    onValueChange={setStatusFilter}
                  >
                    <SelectTrigger className="w-[140px] h-10 flex-shrink-0">
                      <SelectValue placeholder="Trạng thái" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tất cả</SelectItem>
                      <SelectItem value="active">Hoạt động</SelectItem>
                      <SelectItem value="locked">Khóa</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>

            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Đang tải dữ liệu...</span>
              </div>
            )}

            {/* Desktop Table View - Hidden on mobile */}
            {!isLoading && (
              <div className="hidden lg:block">
                <div className="rounded-lg border overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[80px]">ID</TableHead>
                          <TableHead>Tên POS</TableHead>
                          <TableHead>Ngân hàng</TableHead>
                          <TableHead className="hidden md:table-cell">Phí Visa</TableHead>
                          <TableHead className="hidden md:table-cell">Phí Master</TableHead>
                          <TableHead className="hidden md:table-cell">Phí Ngân hàng</TableHead>
                          <TableHead>Số dư</TableHead>
                          <TableHead>Trạng thái</TableHead>
                          <TableHead className="hidden md:table-cell">Ngày tạo</TableHead>
                          <TableHead className="text-right">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredPOS.map((pos) => (
                          <TableRow key={pos.id} className="group hover:bg-muted/50 transition-colors">
                            <TableCell className="font-medium">{pos.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <CreditCard className="h-4 w-4 text-primary mr-2" />
                                <span>{pos.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {pos.bank_id ? (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                        {pos.Bank?.name || "Không xác định"}
                                      </Badge>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <span>Mã ngân hàng: {pos.bank_id}</span>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              ) : (
                                <span className="text-muted-foreground text-sm">Không có</span>
                              )}
                            </TableCell>
                            <TableCell className="hidden md:table-cell">{formatPercentage(pos.visa_fee)}%</TableCell>
                            <TableCell className="hidden md:table-cell">{formatPercentage(pos.master_fee)}%</TableCell>
                            <TableCell className="hidden md:table-cell">{formatPercentage(pos.fee_bank)}%</TableCell>
                            <TableCell>
                              {(() => {
                                const balance = pos.balance || 0;
                                if (balance > 0) {
                                  return <span className="font-medium text-rose-500">{formatCurrency(balance)}</span>;
                                } else if (balance < 0) {
                                  return <span className="font-medium text-emerald-600">{formatCurrency(balance)}</span>;
                                } else {
                                  return <span className="font-medium text-muted-foreground">{formatCurrency(balance)}</span>;
                                }
                              })()}
                            </TableCell>
                            <TableCell>
                              {pos.status === 'active' ? (
                                <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                                  <CheckCircle className="h-3 w-3 mr-1" /> Hoạt động
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
                                  <AlertCircle className="h-3 w-3 mr-1" /> Bị khóa
                                </Badge>
                              )}
                            </TableCell>
                            <TableCell className="hidden md:table-cell text-muted-foreground">
                              <div className="flex items-center">
                                <CalendarIcon className="mr-1 h-3 w-3" />
                                {formatDate(pos.created_at)}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => handleEditPOS(pos)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}

                        {filteredPOS.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={10} className="text-center py-6 text-muted-foreground">
                              Không có dữ liệu POS
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}

            {/* Mobile Card View - Visible on mobile and tablet */}
            {!isLoading && (
              <div className="lg:hidden">
                {filteredPOS.length > 0 ? (
                  <div className="space-y-4">
                    {filteredPOS.map((pos) => (
                      <MobileCardView key={pos.id} pos={pos} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Không có dữ liệu POS
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Dialog form */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editMode ? "Chỉnh sửa thông tin POS" : "Thêm POS mới"}
            </DialogTitle>
            <DialogDescription>
              Điền thông tin chi tiết về POS
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="posName">Tên POS <span className="text-red-500">*</span></Label>
                <Input
                  id="posName"
                  value={selectedPOS.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Nhập tên POS (VD: VP-Trang)"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankId">Ngân hàng</Label>
                <div className="flex items-center space-x-2">
                  <div className="flex-1">
                    <Select
                      value={selectedPOS.bank_id?.toString() || "0"}
                      onValueChange={(value) => handleInputChange("bank_id", value === "0" ? undefined : Number(value))}
                    >
                      <SelectTrigger id="bankId">
                        <SelectValue placeholder="Chọn ngân hàng" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">Không có</SelectItem>
                        {banks.map((bank) => (
                          <SelectItem key={bank.id} value={bank.id.toString()}>
                            {bank.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setShowAddBankDialog(true)}
                    title="Thêm ngân hàng mới"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 items-center gap-4">
              <div className="space-y-2">
                <Label htmlFor="feeVisa">Phí Visa (%)</Label>
                <Input
                  id="feeVisa"
                  value={selectedPOS.visa_fee.toString()}
                  onChange={(e) => handleInputChange("visa_fee", e.target.value)}
                  placeholder="1.3"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="feeMaster">Phí Master (%)</Label>
                <Input
                  id="feeMaster"
                  value={selectedPOS.master_fee.toString()}
                  onChange={(e) => handleInputChange("master_fee", e.target.value)}
                  placeholder="1.3"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 items-center gap-4">
              <div className="space-y-2">
                <Label htmlFor="feeJCB">Phí JCB (%)</Label>
                <Input
                  id="feeJCB"
                  value={selectedPOS.jcb_fee.toString()}
                  onChange={(e) => handleInputChange("jcb_fee", e.target.value)}
                  placeholder="1.3"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="feeNapas">Phí Napas (%)</Label>
                <Input
                  id="feeNapas"
                  value={selectedPOS.napas_fee.toString()}
                  onChange={(e) => handleInputChange("napas_fee", e.target.value)}
                  placeholder="1.0"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 items-center gap-4">
              <div className="space-y-2">
                <Label htmlFor="feeBank">Phí Ngân hàng cùng ngân hàng (%)</Label>
                <Input
                  id="feeBank"
                  value={selectedPOS.fee_bank?.toString() || "0.5"}
                  onChange={(e) => handleInputChange("fee_bank", e.target.value)}
                  placeholder="0.5"
                />
                <p className="text-xs text-muted-foreground">
                  Áp dụng khi thẻ và POS cùng ngân hàng
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dailyLimit">Giới hạn giao dịch ngày</Label>
                <Input
                  id="dailyLimit"
                  value={formatCurrency(selectedPOS.daily_limit || 0)}
                  onChange={(e) => handleInputChange("daily_limit", e.target.value)}
                  placeholder="100,000,000"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="note">Ghi chú</Label>
              <Input
                id="note"
                value={selectedPOS.note || ""}
                onChange={(e) => handleInputChange("note", e.target.value)}
                placeholder="Nhập ghi chú về POS này (không bắt buộc)"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="status"
                checked={selectedPOS.status === 'active'}
                onCheckedChange={(checked) => handleInputChange("status", checked)}
              />
              <Label htmlFor="status" className="font-medium cursor-pointer">
                {selectedPOS.status === 'active' ? (
                  <span className="text-green-600 flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Đang hoạt động
                  </span>
                ) : (
                  <span className="text-red-600 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Bị khóa
                  </span>
                )}
              </Label>
            </div>

            {editMode && (
              <div className="space-y-2">
                <Label>Số dư hiện tại</Label>
                <div className="px-3 py-2 rounded-md bg-muted font-medium">
                  {formatCurrency(selectedPOS.balance || 0)}
                </div>
                <p className="text-sm text-muted-foreground">
                  (Số dư được tính tự động từ các giao dịch quẹt thẻ)
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
              Hủy
            </Button>
            <Button type="button" onClick={handleSubmit} disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editMode ? "Cập nhật" : "Tạo mới"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog thêm ngân hàng mới */}
      <Dialog open={showAddBankDialog} onOpenChange={setShowAddBankDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Thêm ngân hàng mới</DialogTitle>
            <DialogDescription>
              Nhập tên ngân hàng mới để thêm vào hệ thống
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Tên ngân hàng</Label>
              <Input
                id="bankName"
                value={newBankName}
                onChange={(e) => setNewBankName(e.target.value)}
                placeholder="Nhập tên ngân hàng (VD: Techcombank)"
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowAddBankDialog(false)}>
              Hủy
            </Button>
            <Button type="button" onClick={handleAddBank} disabled={addingBank}>
              {addingBank && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Thêm ngân hàng
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
}
