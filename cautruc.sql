-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON> chủ: localhost
-- Th<PERSON><PERSON> gian đã tạo: Th6 02, 2025 lúc 12:52 PM
-- <PERSON><PERSON><PERSON> bản m<PERSON>hụ<PERSON> vụ: 11.4.5-MariaDB-log
-- <PERSON><PERSON><PERSON> bản PHP: 8.2.27

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> sở dữ liệu: `tinphatcredit`
--

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `banks`
--

CREATE TABLE `banks` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `cards`
--

CREATE TABLE `cards` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `card_number` varchar(50) DEFAULT NULL,
  `credit_limit` decimal(15,2) DEFAULT 0.00 COMMENT 'Hạn mức thẻ',
  `statement_date` int(11) DEFAULT NULL COMMENT 'Ngày sao kê (1-31)',
  `payment_date` int(11) DEFAULT NULL COMMENT 'Ngày thanh toán (1-31)',
  `note` text DEFAULT NULL,
  `is_holding_card` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `bank_id` int(11) NOT NULL,
  `account_number` varchar(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `card_attachments`
--

CREATE TABLE `card_attachments` (
  `id` int(11) NOT NULL,
  `card_id` int(11) NOT NULL,
  `file_url` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `card_swipes`
--

CREATE TABLE `card_swipes` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `card_id` int(11) NOT NULL,
  `swipe_date` datetime NOT NULL,
  `amount` decimal(15,2) NOT NULL COMMENT 'Số tiền quẹt',
  `pos_id` int(11) NOT NULL COMMENT 'POS quẹt',
  `bank_fee` decimal(15,2) DEFAULT 0.00 COMMENT 'Phí bank thu',
  `net_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'Số tiền về',
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Bẫy `card_swipes`
--
DELIMITER $$
CREATE TRIGGER `after_card_swipe_delete` AFTER DELETE ON `card_swipes` FOR EACH ROW BEGIN
  -- Lấy thông tin order_type
  DECLARE v_order_type VARCHAR(10);
  DECLARE v_negative_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);

  SELECT order_type, IFNULL(negative_amount, 0), IFNULL(charge_customer_fee, 0)
  INTO v_order_type, v_negative_amount, v_charge_customer_fee
  FROM orders
  WHERE id = OLD.order_id;

  -- Cập nhật orders dựa vào order_type
  IF v_order_type = 'Đáo' THEN
    UPDATE orders
    SET total_swiped = total_swiped - OLD.amount,
        need_to_swipe = total_amount - ((total_swiped - OLD.amount) + negative_amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = OLD.order_id)
    WHERE id = OLD.order_id;
  ELSE
    UPDATE orders
    SET total_swiped = total_swiped - OLD.amount,
        need_to_swipe = total_amount - (total_swiped - OLD.amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = OLD.order_id)
    WHERE id = OLD.order_id;
  END IF;

  -- Cập nhật balance của POS
  UPDATE pos_terminals
  SET balance = balance - OLD.net_amount
  WHERE id = OLD.pos_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_card_swipe_insert` AFTER INSERT ON `card_swipes` FOR EACH ROW BEGIN
  -- Lấy thông tin order_type
  DECLARE v_order_type VARCHAR(10);
  DECLARE v_negative_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);

  SELECT order_type, IFNULL(negative_amount, 0), IFNULL(charge_customer_fee, 0)
  INTO v_order_type, v_negative_amount, v_charge_customer_fee
  FROM orders
  WHERE id = NEW.order_id;

  -- Cập nhật orders dựa vào order_type
  IF v_order_type = 'Đáo' THEN
    UPDATE orders
    SET total_swiped = total_swiped + NEW.amount,
        need_to_swipe = total_amount - ((total_swiped + NEW.amount) + negative_amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = NEW.order_id)
    WHERE id = NEW.order_id;
  ELSE
    UPDATE orders
    SET total_swiped = total_swiped + NEW.amount,
        need_to_swipe = total_amount - (total_swiped + NEW.amount),
        profit = v_charge_customer_fee - (SELECT COALESCE(SUM(bank_fee), 0) FROM card_swipes WHERE order_id = NEW.order_id)
    WHERE id = NEW.order_id;
  END IF;

  -- Cập nhật balance của POS
  UPDATE pos_terminals
  SET balance = balance + NEW.net_amount
  WHERE id = NEW.pos_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `customer_fee` decimal(5,2) DEFAULT 1.70 COMMENT 'Phí thu khách (%) thẻ quốc tế',
  `outstanding_fee` decimal(15,2) DEFAULT 0.00 COMMENT 'Phí đang nợ',
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `customer_fee_2` decimal(5,2) DEFAULT NULL COMMENT 'Phí % thẻ nội địa'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `financial_reports`
--

CREATE TABLE `financial_reports` (
  `id` int(11) NOT NULL,
  `report_date` date NOT NULL,
  `opening_balance` decimal(15,2) DEFAULT 0.00 COMMENT 'Tiền đầu ngày',
  `total_income` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng thu',
  `total_transfer` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng chuyển',
  `closing_balance` decimal(15,2) DEFAULT 0.00 COMMENT 'Tồn cuối',
  `total_returned` decimal(15,2) DEFAULT 0.00 COMMENT 'Tiền đã về',
  `total_swiped` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng quẹt',
  `unprocessed_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'Số tiền chưa quẹt',
  `profit` decimal(15,2) DEFAULT 0.00 COMMENT 'Lợi nhuận',
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `kpi_distribution_weights`
--

CREATE TABLE `kpi_distribution_weights` (
  `id` int(11) NOT NULL,
  `kpi_setting_id` int(11) NOT NULL,
  `period_index` int(11) NOT NULL,
  `weight` decimal(5,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `kpi_settings`
--

CREATE TABLE `kpi_settings` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `target_type` enum('Daily','Weekly','Monthly','Quarterly','Yearly') NOT NULL,
  `metric_type` enum('Amount','Count','Percentage') NOT NULL,
  `category` enum('Revenue','Profit','Transaction','Customer','POS') NOT NULL,
  `target_value` decimal(15,2) NOT NULL,
  `parent_kpi_id` int(11) DEFAULT NULL,
  `distribution_method` enum('Equal','Weighted','Progressive') DEFAULT 'Equal',
  `rollover_enabled` tinyint(1) DEFAULT 0,
  `active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `kpi_tracking`
--

CREATE TABLE `kpi_tracking` (
  `id` int(11) NOT NULL,
  `kpi_setting_id` int(11) NOT NULL,
  `period_start_date` date NOT NULL,
  `period_end_date` date NOT NULL,
  `target_value` decimal(15,2) NOT NULL,
  `rollover_amount` decimal(15,2) DEFAULT 0.00,
  `adjusted_target` decimal(15,2) DEFAULT NULL,
  `actual_value` decimal(15,2) NOT NULL DEFAULT 0.00,
  `achievement_percentage` decimal(5,2) DEFAULT NULL,
  `status` enum('Pending','Achieved','Missed') NOT NULL DEFAULT 'Pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `money_returns`
--

CREATE TABLE `money_returns` (
  `id` int(11) NOT NULL,
  `pos_id` int(11) DEFAULT NULL COMMENT 'POS tiền về',
  `customer_id` int(11) DEFAULT NULL COMMENT 'Khách hàng trả nợ',
  `amount` decimal(15,2) NOT NULL COMMENT 'Số tiền',
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `order_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Bẫy `money_returns`
--
DELIMITER $$
CREATE TRIGGER `after_money_return_delete` AFTER DELETE ON `money_returns` FOR EACH ROW BEGIN
  -- Cập nhật balance của POS
  IF OLD.pos_id IS NOT NULL THEN
    UPDATE pos_terminals
    SET balance = balance + OLD.amount
    WHERE id = OLD.pos_id;
  END IF;

  -- Cập nhật outstanding_fee của customer
  IF OLD.customer_id IS NOT NULL THEN
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount
    WHERE id = OLD.customer_id;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_money_return_insert` AFTER INSERT ON `money_returns` FOR EACH ROW BEGIN
  -- Cập nhật balance của POS
  IF NEW.pos_id IS NOT NULL THEN
    UPDATE pos_terminals
    SET balance = balance - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  -- Cập nhật outstanding_fee của customer
  IF NEW.customer_id IS NOT NULL THEN
    UPDATE customers
    SET outstanding_fee = outstanding_fee - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_money_return_update` AFTER UPDATE ON `money_returns` FOR EACH ROW BEGIN
  -- Xử lý khi thay đổi pos_id
  IF OLD.pos_id IS NOT NULL AND (NEW.pos_id IS NULL OR NEW.pos_id != OLD.pos_id) THEN
    -- Hoàn lại balance cho POS cũ
    UPDATE pos_terminals
    SET balance = balance + OLD.amount
    WHERE id = OLD.pos_id;
  END IF;

  IF NEW.pos_id IS NOT NULL AND (OLD.pos_id IS NULL OR NEW.pos_id != OLD.pos_id) THEN
    -- Cập nhật balance cho POS mới
    UPDATE pos_terminals
    SET balance = balance - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  -- Xử lý khi thay đổi customer_id
  IF OLD.customer_id IS NOT NULL AND (NEW.customer_id IS NULL OR NEW.customer_id != OLD.customer_id) THEN
    -- Hoàn lại outstanding_fee cho customer cũ
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount
    WHERE id = OLD.customer_id;
  END IF;

  IF NEW.customer_id IS NOT NULL AND (OLD.customer_id IS NULL OR NEW.customer_id != OLD.customer_id) THEN
    -- Cập nhật outstanding_fee cho customer mới
    UPDATE customers
    SET outstanding_fee = outstanding_fee - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;

  -- Xử lý khi chỉ thay đổi amount
  IF OLD.pos_id IS NOT NULL AND NEW.pos_id = OLD.pos_id AND OLD.amount != NEW.amount THEN
    -- Cập nhật balance cho POS
    UPDATE pos_terminals
    SET balance = balance + OLD.amount - NEW.amount
    WHERE id = NEW.pos_id;
  END IF;

  IF OLD.customer_id IS NOT NULL AND NEW.customer_id = OLD.customer_id AND OLD.amount != NEW.amount THEN
    -- Cập nhật outstanding_fee cho customer
    UPDATE customers
    SET outstanding_fee = outstanding_fee + OLD.amount - NEW.amount
    WHERE id = NEW.customer_id;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_money_return_insert` BEFORE INSERT ON `money_returns` FOR EACH ROW BEGIN
  -- Đảm bảo khi customer_id có giá trị, pos_id phải là NULL
  IF NEW.customer_id IS NOT NULL AND NEW.pos_id IS NOT NULL THEN
    SIGNAL SQLSTATE '45000'
    SET MESSAGE_TEXT = 'Không thể có cả pos_id và customer_id cùng lúc';
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_money_return_update` BEFORE UPDATE ON `money_returns` FOR EACH ROW BEGIN
  -- Đảm bảo khi customer_id có giá trị, pos_id phải là NULL
  IF NEW.customer_id IS NOT NULL AND NEW.pos_id IS NOT NULL THEN
    SIGNAL SQLSTATE '45000'
    SET MESSAGE_TEXT = 'Không thể có cả pos_id và customer_id cùng lúc';
  END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `money_transfers`
--

CREATE TABLE `money_transfers` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `transfer_date` datetime NOT NULL,
  `amount` decimal(15,2) NOT NULL COMMENT 'Số tiền chuyển',
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Bẫy `money_transfers`
--
DELIMITER $$
CREATE TRIGGER `after_money_transfer_delete` AFTER DELETE ON `money_transfers` FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = OLD.order_id;

  UPDATE orders
  SET total_transferred = total_transferred - OLD.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred - OLD.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred - OLD.amount)
        ELSE 0
      END
  WHERE id = OLD.order_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_money_transfer_insert` AFTER INSERT ON `money_transfers` FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  -- Lấy thông tin đơn hàng
  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = NEW.order_id;

  -- Cập nhật đơn hàng
  UPDATE orders
  SET total_transferred = total_transferred + NEW.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred + NEW.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred + NEW.amount)
        ELSE 0
      END
  WHERE id = NEW.order_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_money_transfer_update` AFTER UPDATE ON `money_transfers` FOR EACH ROW BEGIN
  DECLARE v_order_type ENUM('Đáo', 'Rút');
  DECLARE v_total_amount DECIMAL(15,2);
  DECLARE v_charge_customer_fee DECIMAL(15,2);
  DECLARE v_debt_deduction DECIMAL(15,2);

  -- Lấy thông tin đơn hàng
  SELECT order_type, total_amount, charge_customer_fee, IFNULL(debt_deduction, 0)
  INTO v_order_type, v_total_amount, v_charge_customer_fee, v_debt_deduction
  FROM orders WHERE id = NEW.order_id;

  -- Cập nhật đơn hàng
  UPDATE orders
  SET total_transferred = total_transferred - OLD.amount + NEW.amount,
      need_to_transfer = CASE
        WHEN v_order_type = 'Đáo' THEN v_total_amount - (total_transferred - OLD.amount + NEW.amount)
        WHEN v_order_type = 'Rút' THEN v_total_amount - IFNULL(v_charge_customer_fee, 0) - v_debt_deduction - (total_transferred - OLD.amount + NEW.amount)
        ELSE 0
      END
  WHERE id = NEW.order_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `card_id` int(11) DEFAULT NULL COMMENT 'ID thẻ sử dụng',
  `order_type` enum('Đáo','Rút') NOT NULL DEFAULT 'Đáo' COMMENT 'Loại hình giao dịch',
  `total_amount` decimal(15,2) NOT NULL COMMENT 'Tổng tiền cần làm',
  `fee_percentage` decimal(5,2) DEFAULT 1.70 COMMENT 'Phí (%)',
  `total_swiped` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng đã quẹt',
  `need_to_swipe` decimal(15,2) DEFAULT 0.00 COMMENT 'Cần quẹt thẻ',
  `total_transferred` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng đã chuyển',
  `need_to_transfer` decimal(15,2) DEFAULT 0.00 COMMENT 'Cần chuyển tiền',
  `total_fee` decimal(15,2) DEFAULT 0.00 COMMENT 'Tổng phí',
  `profit` decimal(15,2) DEFAULT 0.00 COMMENT 'Lợi nhuận',
  `charge_customer_fee` decimal(15,2) DEFAULT 1.00 COMMENT 'Tính phí khách hàng',
  `negative_amount` decimal(15,2) DEFAULT 0.00 COMMENT 'Tiền âm (tiền bị treo hoặc thiếu)',
  `debt_deduction` decimal(15,2) DEFAULT 0.00 COMMENT 'Trừ nợ (chỉ áp dụng cho đơn hàng Rút)',
  `status` enum('pending','processing','completed') DEFAULT 'pending',
  `note` text DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Bẫy `orders`
--
DELIMITER $$
CREATE TRIGGER `after_order_status_update` AFTER UPDATE ON `orders` FOR EACH ROW BEGIN
  -- Khai báo biến ở đầu khối BEGIN
  DECLARE v_customer_id INT;
  DECLARE v_customer_name VARCHAR(100);
  DECLARE v_money_return_exists INT DEFAULT 0;

  -- Tạo money_returns cho đơn hàng "Rút" có giá trị "Trừ Nợ" khi chuyển sang trạng thái "completed"
  IF NEW.status = 'completed' AND OLD.status != 'completed' AND NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 THEN
    -- Kiểm tra xem đã có bản ghi money_returns cho đơn hàng này chưa
    SELECT COUNT(*) INTO v_money_return_exists
    FROM money_returns
    WHERE order_id = NEW.id;
    
    -- Chỉ tạo bản ghi nếu chưa tồn tại
    IF v_money_return_exists = 0 THEN
      -- Lấy customer_id từ card_id
      SELECT customer_id INTO v_customer_id
      FROM cards
      WHERE id = NEW.card_id;
      
      -- Tạo bản ghi money_returns
      INSERT INTO money_returns (customer_id, amount, note, user_id, order_id, created_at, updated_at)
      VALUES (v_customer_id, NEW.debt_deduction, CONCAT('Trừ nợ từ đơn hàng Rút #', NEW.id), NEW.user_id, NEW.id, NOW(), NOW());
    END IF;
  END IF;
  
  -- Xử lý khi trạng thái đơn hàng thay đổi từ completed sang khác
  IF OLD.status = 'completed' AND NEW.status != 'completed' AND OLD.order_type = 'Rút' AND IFNULL(OLD.debt_deduction, 0) > 0 THEN
    -- Xóa bản ghi money_returns liên quan
    DELETE FROM money_returns
    WHERE order_id = NEW.id;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_order_delete` BEFORE DELETE ON `orders` FOR EACH ROW BEGIN
  DECLARE v_customer_id INT;
  
  -- Lấy customer_id từ card_id của đơn hàng
  SELECT customer_id INTO v_customer_id
  FROM cards
  WHERE id = OLD.card_id;
  
  -- Xử lý dựa vào loại đơn hàng
  IF OLD.order_type = 'Đáo' THEN
    -- Hoàn lại outstanding_fee cho customer
    IF OLD.card_id IS NOT NULL AND OLD.total_fee > 0 THEN
      UPDATE customers
      SET outstanding_fee = outstanding_fee - OLD.total_fee
      WHERE id = v_customer_id;
    END IF;
    
    -- Cập nhật balance của POS cho đơn hàng Đáo
    UPDATE pos_terminals p
    JOIN (
      SELECT pos_id, SUM(net_amount) as total_net_amount
      FROM card_swipes
      WHERE order_id = OLD.id
      GROUP BY pos_id
    ) cs ON p.id = cs.pos_id
    SET p.balance = p.balance - cs.total_net_amount;
  ELSEIF OLD.order_type = 'Rút' THEN
    -- Xóa các bản ghi money_returns liên quan đến đơn hàng "Rút" có Trừ Nợ
    IF IFNULL(OLD.debt_deduction, 0) > 0 THEN
      DELETE FROM money_returns
      WHERE order_id = OLD.id;
    END IF;
    
    -- Cập nhật balance của POS cho đơn hàng Rút
    UPDATE pos_terminals p
    JOIN (
      SELECT pos_id, SUM(net_amount) as total_net_amount
      FROM card_swipes
      WHERE order_id = OLD.id
      GROUP BY pos_id
    ) cs ON p.id = cs.pos_id
    SET p.balance = p.balance - cs.total_net_amount;
  END IF;
  
  -- Lưu ý:
  -- 1. card_swipes và money_transfers sẽ tự động bị xóa do ON DELETE CASCADE
  -- 2. POS.balance đã được cập nhật trực tiếp trong trigger này
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_order_insert` BEFORE INSERT ON `orders` FOR EACH ROW BEGIN
  -- Khai báo biến ở đầu khối BEGIN
  DECLARE v_outstanding_fee DECIMAL(15,2);
  DECLARE v_customer_id INT;
  
  -- Tính toán need_to_swipe dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_swipe = NEW.total_amount - (NEW.total_swiped + IFNULL(NEW.negative_amount, 0));
  ELSE
    SET NEW.need_to_swipe = NEW.total_amount - NEW.total_swiped;
  END IF;

  -- Tính toán need_to_transfer dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_transfer = NEW.total_amount - NEW.total_transferred;
  ELSEIF NEW.order_type = 'Rút' THEN
    SET NEW.need_to_transfer = NEW.total_amount - IFNULL(NEW.charge_customer_fee, 0) - IFNULL(NEW.debt_deduction, 0) - NEW.total_transferred;
    -- Đảm bảo đơn hàng "Rút" không có negative_amount
    SET NEW.negative_amount = 0;
  END IF;

  -- Tính toán total_fee cho tất cả loại đơn hàng
  SET NEW.total_fee = IFNULL(NEW.charge_customer_fee, 0) + IFNULL(NEW.negative_amount, 0);

  -- Tính toán profit ban đầu
  -- Lưu ý: profit sẽ được cập nhật lại trong trigger after_card_swipe_insert/update
  SET NEW.profit = IFNULL(NEW.charge_customer_fee, 0);

  -- Cập nhật outstanding_fee của customer thông qua card_id
  -- Chỉ áp dụng cho đơn hàng "Đáo", không áp dụng cho đơn hàng "Rút"
  IF NEW.card_id IS NOT NULL AND NEW.order_type = 'Đáo' AND NEW.total_fee > 0 THEN
    UPDATE customers c
    JOIN cards cd ON c.id = cd.customer_id
    SET c.outstanding_fee = c.outstanding_fee + NEW.total_fee
    WHERE cd.id = NEW.card_id;
  END IF;
  
  -- Kiểm tra outstanding_fee > 0 khi tạo đơn hàng "Rút" có giá trị "Trừ Nợ"
  IF NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 THEN
    -- Lấy customer_id từ card_id
    SELECT customer_id INTO v_customer_id
    FROM cards
    WHERE id = NEW.card_id;
    
    -- Lấy outstanding_fee của customer
    SELECT outstanding_fee INTO v_outstanding_fee
    FROM customers
    WHERE id = v_customer_id;
    
    -- Kiểm tra outstanding_fee > 0
    IF v_outstanding_fee <= 0 THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Khách hàng không có nợ phí, không thể áp dụng Trừ Nợ';
    END IF;
    
    -- Kiểm tra debt_deduction <= (total_swiped - charge_customer_fee)
    -- Lưu ý: Đối với đơn hàng mới, total_swiped thường là 0, nên điều kiện này sẽ ngăn người dùng nhập giá trị Trừ Nợ > 0
    -- Người dùng cần quẹt thẻ trước khi có thể áp dụng Trừ Nợ
    IF NEW.debt_deduction > (NEW.total_swiped - IFNULL(NEW.charge_customer_fee, 0)) THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Số tiền Trừ Nợ không được vượt quá (Tổng đã quẹt - Tiền phí)';
    END IF;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_order_update` BEFORE UPDATE ON `orders` FOR EACH ROW BEGIN
  -- Khai báo tất cả biến ở đầu khối BEGIN
  DECLARE v_outstanding_fee DECIMAL(15,2);
  DECLARE v_customer_id INT;
  DECLARE v_bank_fee_sum DECIMAL(15,2);
  DECLARE v_money_return_id INT;
  
  -- Tính tổng bank_fee từ card_swipes
  SELECT COALESCE(SUM(bank_fee), 0) INTO v_bank_fee_sum
  FROM card_swipes
  WHERE order_id = NEW.id;
  
  -- Tính toán profit
  SET NEW.profit = IFNULL(NEW.charge_customer_fee, 0) - v_bank_fee_sum;
  
  -- Tính toán need_to_swipe dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_swipe = NEW.total_amount - (NEW.total_swiped + IFNULL(NEW.negative_amount, 0));
  ELSE
    SET NEW.need_to_swipe = NEW.total_amount - NEW.total_swiped;
  END IF;

  -- Tính toán need_to_transfer dựa vào order_type
  IF NEW.order_type = 'Đáo' THEN
    SET NEW.need_to_transfer = NEW.total_amount - NEW.total_transferred;
  ELSEIF NEW.order_type = 'Rút' THEN
    SET NEW.need_to_transfer = NEW.total_amount - IFNULL(NEW.charge_customer_fee, 0) - IFNULL(NEW.debt_deduction, 0) - NEW.total_transferred;
    SET NEW.negative_amount = 0;
  END IF;

  -- Tính toán total_fee cho tất cả loại đơn hàng
  SET NEW.total_fee = IFNULL(NEW.charge_customer_fee, 0) + IFNULL(NEW.negative_amount, 0);

  -- Xử lý khi thay đổi card_id hoặc charge_customer_fee
  -- Chỉ áp dụng cho đơn hàng "Đáo", không áp dụng cho đơn hàng "Rút"
  IF ((OLD.card_id IS NOT NULL AND OLD.card_id != NEW.card_id) OR
      (OLD.charge_customer_fee != NEW.charge_customer_fee) OR
      (OLD.total_fee != NEW.total_fee) OR
      (OLD.order_type != NEW.order_type)) AND
     (OLD.order_type = 'Đáo' OR NEW.order_type = 'Đáo') THEN

    -- Hoàn lại outstanding_fee cho customer cũ (chỉ khi đơn hàng cũ là "Đáo")
    IF OLD.card_id IS NOT NULL AND OLD.order_type = 'Đáo' AND OLD.total_fee > 0 THEN
      UPDATE customers c
      JOIN cards cd ON c.id = cd.customer_id
      SET c.outstanding_fee = c.outstanding_fee - OLD.total_fee
      WHERE cd.id = OLD.card_id;
    END IF;

    -- Cập nhật outstanding_fee cho customer mới (chỉ khi đơn hàng mới là "Đáo")
    IF NEW.card_id IS NOT NULL AND NEW.order_type = 'Đáo' AND NEW.total_fee > 0 THEN
      UPDATE customers c
      JOIN cards cd ON c.id = cd.customer_id
      SET c.outstanding_fee = c.outstanding_fee + NEW.total_fee
      WHERE cd.id = NEW.card_id;
    END IF;
  END IF;
  
  -- Kiểm tra outstanding_fee > 0 khi cập nhật đơn hàng "Rút" có giá trị "Trừ Nợ"
  IF NEW.order_type = 'Rút' AND NEW.card_id IS NOT NULL AND IFNULL(NEW.debt_deduction, 0) > 0 AND 
     (IFNULL(OLD.debt_deduction, 0) != IFNULL(NEW.debt_deduction, 0) OR OLD.card_id != NEW.card_id) THEN
    -- Lấy customer_id từ card_id
    SELECT customer_id INTO v_customer_id
    FROM cards
    WHERE id = NEW.card_id;
    
    -- Lấy outstanding_fee của customer
    SELECT outstanding_fee INTO v_outstanding_fee
    FROM customers
    WHERE id = v_customer_id;
    
    -- Kiểm tra outstanding_fee > 0
    IF v_outstanding_fee <= 0 THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Khách hàng không có nợ phí, không thể áp dụng Trừ Nợ';
    END IF;
    
    -- Kiểm tra debt_deduction <= (total_swiped - charge_customer_fee)
    IF NEW.debt_deduction > (NEW.total_swiped - IFNULL(NEW.charge_customer_fee, 0)) THEN
      SIGNAL SQLSTATE '45000'
      SET MESSAGE_TEXT = 'Số tiền Trừ Nợ không được vượt quá (Tổng đã quẹt - Tiền phí)';
    END IF;
    
    -- Xử lý khi debt_deduction thay đổi và đơn hàng đã ở trạng thái completed
    IF NEW.status = 'completed' AND OLD.status = 'completed' AND IFNULL(OLD.debt_deduction, 0) != IFNULL(NEW.debt_deduction, 0) THEN
      -- Tìm bản ghi money_returns hiện có
      SELECT id INTO v_money_return_id
      FROM money_returns
      WHERE order_id = NEW.id
      LIMIT 1;
      
      -- Nếu đã có bản ghi money_returns
      IF v_money_return_id IS NOT NULL THEN
        -- Nếu debt_deduction mới > 0
        IF IFNULL(NEW.debt_deduction, 0) > 0 THEN
          -- Cập nhật bản ghi money_returns hiện có
          UPDATE money_returns
          SET amount = NEW.debt_deduction,
              updated_at = NOW()
          WHERE id = v_money_return_id;
        -- Nếu debt_deduction mới = 0
        ELSE
          -- Xóa bản ghi money_returns hiện có
          DELETE FROM money_returns
          WHERE id = v_money_return_id;
        END IF;
      -- Nếu chưa có bản ghi money_returns nhưng debt_deduction mới > 0
      ELSEIF IFNULL(NEW.debt_deduction, 0) > 0 THEN
        -- Tạo bản ghi money_returns mới
        INSERT INTO money_returns (customer_id, amount, note, user_id, order_id, created_at, updated_at)
        VALUES (v_customer_id, NEW.debt_deduction, CONCAT('Trừ nợ từ đơn hàng Rút #', NEW.id), NEW.user_id, NEW.id, NOW(), NOW());
      END IF;
    END IF;
  END IF;
  
  -- Tự động cập nhật trạng thái đơn hàng dựa vào need_to_swipe và need_to_transfer
  IF NEW.order_type = 'Đáo' THEN
    IF NEW.need_to_swipe <= 0 AND NEW.need_to_transfer <= 0 THEN
      SET NEW.status = 'completed';
    ELSEIF NEW.total_swiped > 0 OR NEW.total_transferred > 0 THEN
      SET NEW.status = 'processing';
    END IF;
  ELSEIF NEW.order_type = 'Rút' THEN
    IF NEW.need_to_swipe <= 0 AND NEW.need_to_transfer <= 0 THEN
      SET NEW.status = 'completed';
    ELSEIF NEW.total_swiped > 0 OR NEW.total_transferred > 0 THEN
      SET NEW.status = 'processing';
    END IF;
  END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `pos_terminals`
--

CREATE TABLE `pos_terminals` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT 'Tên POS',
  `visa_fee` decimal(10,4) DEFAULT 1.3000 COMMENT 'Phí Visa (%)',
  `master_fee` decimal(10,4) DEFAULT 1.3000 COMMENT 'Phí Master (%)',
  `jcb_fee` decimal(10,4) DEFAULT 1.3000 COMMENT 'Phí JCB (%)',
  `napas_fee` decimal(10,4) DEFAULT 1.3000 COMMENT 'Phí Napas (%)',
  `balance` decimal(15,2) DEFAULT 0.00 COMMENT 'Tiền đang tồn',
  `user_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `note` text DEFAULT NULL,
  `status` enum('active','locked') NOT NULL DEFAULT 'active',
  `daily_limit` decimal(15,2) NOT NULL DEFAULT **********.00,
  `bank_id` int(11) NOT NULL,
  `fee_bank` decimal(10,4) NOT NULL DEFAULT 0.8800
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `role` enum('admin','user') NOT NULL DEFAULT 'admin',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `user_logs`
--

CREATE TABLE `user_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` enum('CREATE','UPDATE','DELETE','LOGIN','LOGOUT','VIEW') NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc đóng vai cho view `view_cards`
-- (See below for the actual view)
--
CREATE TABLE `view_cards` (
`id` int(11)
,`customer_id` int(11)
,`card_number` varchar(50)
,`bank_name` varchar(255)
,`credit_limit` decimal(15,2)
,`statement_date` int(11)
,`payment_date` int(11)
,`note` text
,`created_at` timestamp
,`updated_at` timestamp
,`customer_name` varchar(100)
,`customer_phone` varchar(20)
);

-- --------------------------------------------------------

--
-- Cấu trúc đóng vai cho view `view_card_swipes`
-- (See below for the actual view)
--
CREATE TABLE `view_card_swipes` (
`id` int(11)
,`order_id` int(11)
,`card_id` int(11)
,`swipe_date` datetime
,`amount` decimal(15,2)
,`pos_id` int(11)
,`bank_fee` decimal(15,2)
,`net_amount` decimal(15,2)
,`note` text
,`user_id` int(11)
,`created_at` timestamp
,`updated_at` timestamp
,`order_type` enum('Đáo','Rút')
,`card_number` varchar(50)
,`bank_name` varchar(255)
,`pos_name` varchar(100)
,`customer_name` varchar(100)
);

-- --------------------------------------------------------

--
-- Cấu trúc đóng vai cho view `view_money_returns`
-- (See below for the actual view)
--
CREATE TABLE `view_money_returns` (
`id` int(11)
,`pos_id` int(11)
,`customer_id` int(11)
,`amount` decimal(15,2)
,`note` text
,`user_id` int(11)
,`created_at` timestamp
,`updated_at` timestamp
,`order_id` int(11)
,`pos_name` varchar(100)
,`customer_name` varchar(100)
);

-- --------------------------------------------------------

--
-- Cấu trúc đóng vai cho view `view_money_transfers`
-- (See below for the actual view)
--
CREATE TABLE `view_money_transfers` (
`id` int(11)
,`order_id` int(11)
,`transfer_date` datetime
,`amount` decimal(15,2)
,`note` text
,`user_id` int(11)
,`created_at` timestamp
,`updated_at` timestamp
,`order_type` enum('Đáo','Rút')
,`customer_name` varchar(100)
,`card_number` varchar(50)
,`bank_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Cấu trúc đóng vai cho view `view_orders`
-- (See below for the actual view)
--
CREATE TABLE `view_orders` (
`id` int(11)
,`card_id` int(11)
,`order_type` enum('Đáo','Rút')
,`total_amount` decimal(15,2)
,`fee_percentage` decimal(5,2)
,`total_swiped` decimal(15,2)
,`need_to_swipe` decimal(15,2)
,`total_transferred` decimal(15,2)
,`need_to_transfer` decimal(15,2)
,`total_fee` decimal(15,2)
,`profit` decimal(15,2)
,`charge_customer_fee` decimal(15,2)
,`negative_amount` decimal(15,2)
,`status` enum('pending','processing','completed')
,`note` text
,`user_id` int(11)
,`created_at` timestamp
,`updated_at` timestamp
,`customer_id` int(11)
,`customer_name` varchar(100)
,`customer_phone` varchar(20)
,`card_number` varchar(50)
,`bank_name` varchar(255)
);

-- --------------------------------------------------------

--
-- Cấu trúc cho view `view_cards`
--
DROP TABLE IF EXISTS `view_cards`;

CREATE ALGORITHM=UNDEFINED DEFINER=`thedidokurl_uovk`@`localhost` SQL SECURITY DEFINER VIEW `view_cards`  AS SELECT `c`.`id` AS `id`, `c`.`customer_id` AS `customer_id`, `c`.`card_number` AS `card_number`, `b`.`name` AS `bank_name`, `c`.`credit_limit` AS `credit_limit`, `c`.`statement_date` AS `statement_date`, `c`.`payment_date` AS `payment_date`, `c`.`note` AS `note`, `c`.`created_at` AS `created_at`, `c`.`updated_at` AS `updated_at`, `cust`.`name` AS `customer_name`, `cust`.`phone` AS `customer_phone` FROM ((`cards` `c` join `customers` `cust` on(`c`.`customer_id` = `cust`.`id`)) join `banks` `b` on(`c`.`bank_id` = `b`.`id`)) ;

-- --------------------------------------------------------

--
-- Cấu trúc cho view `view_card_swipes`
--
DROP TABLE IF EXISTS `view_card_swipes`;

CREATE ALGORITHM=UNDEFINED DEFINER=`thedidokurl_uovk`@`localhost` SQL SECURITY DEFINER VIEW `view_card_swipes`  AS SELECT `cs`.`id` AS `id`, `cs`.`order_id` AS `order_id`, `cs`.`card_id` AS `card_id`, `cs`.`swipe_date` AS `swipe_date`, `cs`.`amount` AS `amount`, `cs`.`pos_id` AS `pos_id`, `cs`.`bank_fee` AS `bank_fee`, `cs`.`net_amount` AS `net_amount`, `cs`.`note` AS `note`, `cs`.`user_id` AS `user_id`, `cs`.`created_at` AS `created_at`, `cs`.`updated_at` AS `updated_at`, `o`.`order_type` AS `order_type`, `c`.`card_number` AS `card_number`, `b`.`name` AS `bank_name`, `p`.`name` AS `pos_name`, `cust`.`name` AS `customer_name` FROM (((((`card_swipes` `cs` join `orders` `o` on(`cs`.`order_id` = `o`.`id`)) join `cards` `c` on(`cs`.`card_id` = `c`.`id`)) join `banks` `b` on(`c`.`bank_id` = `b`.`id`)) join `pos_terminals` `p` on(`cs`.`pos_id` = `p`.`id`)) join `customers` `cust` on(`c`.`customer_id` = `cust`.`id`)) ;

-- --------------------------------------------------------

--
-- Cấu trúc cho view `view_money_returns`
--
DROP TABLE IF EXISTS `view_money_returns`;

CREATE ALGORITHM=UNDEFINED DEFINER=`thedidokurl_uovk`@`localhost` SQL SECURITY DEFINER VIEW `view_money_returns`  AS SELECT `mr`.`id` AS `id`, `mr`.`pos_id` AS `pos_id`, `mr`.`customer_id` AS `customer_id`, `mr`.`amount` AS `amount`, `mr`.`note` AS `note`, `mr`.`user_id` AS `user_id`, `mr`.`created_at` AS `created_at`, `mr`.`updated_at` AS `updated_at`, `mr`.`order_id` AS `order_id`, `p`.`name` AS `pos_name`, `c`.`name` AS `customer_name` FROM ((`money_returns` `mr` left join `pos_terminals` `p` on(`mr`.`pos_id` = `p`.`id`)) left join `customers` `c` on(`mr`.`customer_id` = `c`.`id`)) ;

-- --------------------------------------------------------

--
-- Cấu trúc cho view `view_money_transfers`
--
DROP TABLE IF EXISTS `view_money_transfers`;

CREATE ALGORITHM=UNDEFINED DEFINER=`thedidokurl_uovk`@`localhost` SQL SECURITY DEFINER VIEW `view_money_transfers`  AS SELECT `mt`.`id` AS `id`, `mt`.`order_id` AS `order_id`, `mt`.`transfer_date` AS `transfer_date`, `mt`.`amount` AS `amount`, `mt`.`note` AS `note`, `mt`.`user_id` AS `user_id`, `mt`.`created_at` AS `created_at`, `mt`.`updated_at` AS `updated_at`, `o`.`order_type` AS `order_type`, `cust`.`name` AS `customer_name`, `c`.`card_number` AS `card_number`, `b`.`name` AS `bank_name` FROM ((((`money_transfers` `mt` join `orders` `o` on(`mt`.`order_id` = `o`.`id`)) left join `cards` `c` on(`o`.`card_id` = `c`.`id`)) left join `customers` `cust` on(`c`.`customer_id` = `cust`.`id`)) left join `banks` `b` on(`c`.`bank_id` = `b`.`id`)) ;

-- --------------------------------------------------------

--
-- Cấu trúc cho view `view_orders`
--
DROP TABLE IF EXISTS `view_orders`;

CREATE ALGORITHM=UNDEFINED DEFINER=`thedidokurl_uovk`@`localhost` SQL SECURITY DEFINER VIEW `view_orders`  AS SELECT `o`.`id` AS `id`, `o`.`card_id` AS `card_id`, `o`.`order_type` AS `order_type`, `o`.`total_amount` AS `total_amount`, `o`.`fee_percentage` AS `fee_percentage`, `o`.`total_swiped` AS `total_swiped`, `o`.`need_to_swipe` AS `need_to_swipe`, `o`.`total_transferred` AS `total_transferred`, `o`.`need_to_transfer` AS `need_to_transfer`, `o`.`total_fee` AS `total_fee`, `o`.`profit` AS `profit`, `o`.`charge_customer_fee` AS `charge_customer_fee`, `o`.`negative_amount` AS `negative_amount`, `o`.`status` AS `status`, `o`.`note` AS `note`, `o`.`user_id` AS `user_id`, `o`.`created_at` AS `created_at`, `o`.`updated_at` AS `updated_at`, `c`.`customer_id` AS `customer_id`, `cust`.`name` AS `customer_name`, `cust`.`phone` AS `customer_phone`, `c`.`card_number` AS `card_number`, `b`.`name` AS `bank_name` FROM (((`orders` `o` left join `cards` `c` on(`o`.`card_id` = `c`.`id`)) left join `customers` `cust` on(`c`.`customer_id` = `cust`.`id`)) left join `banks` `b` on(`c`.`bank_id` = `b`.`id`)) ;

--
-- Chỉ mục cho các bảng đã đổ
--

--
-- Chỉ mục cho bảng `banks`
--
ALTER TABLE `banks`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `cards`
--
ALTER TABLE `cards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_cards_customer_id` (`customer_id`),
  ADD KEY `idx_cards_is_holding_card` (`is_holding_card`),
  ADD KEY `idx_cards_bank_id` (`bank_id`);

--
-- Chỉ mục cho bảng `card_attachments`
--
ALTER TABLE `card_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `card_id` (`card_id`);

--
-- Chỉ mục cho bảng `card_swipes`
--
ALTER TABLE `card_swipes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_card_swipes_card_id` (`card_id`),
  ADD KEY `idx_card_swipes_pos_id` (`pos_id`),
  ADD KEY `idx_card_swipes_swipe_date` (`swipe_date`),
  ADD KEY `idx_card_swipes_created_at` (`created_at`),
  ADD KEY `idx_card_swipes_order_id` (`order_id`);

--
-- Chỉ mục cho bảng `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customers_user_id` (`user_id`),
  ADD KEY `idx_customers_name` (`name`),
  ADD KEY `idx_customers_phone` (`phone`),
  ADD KEY `idx_customers_created_at` (`created_at`);

--
-- Chỉ mục cho bảng `financial_reports`
--
ALTER TABLE `financial_reports`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `report_date_idx` (`report_date`),
  ADD UNIQUE KEY `unique_report_date` (`report_date`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_financial_reports_report_date` (`report_date`);

--
-- Chỉ mục cho bảng `kpi_distribution_weights`
--
ALTER TABLE `kpi_distribution_weights`
  ADD PRIMARY KEY (`id`),
  ADD KEY `kpi_setting_id` (`kpi_setting_id`);

--
-- Chỉ mục cho bảng `kpi_settings`
--
ALTER TABLE `kpi_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parent_kpi_id` (`parent_kpi_id`);

--
-- Chỉ mục cho bảng `kpi_tracking`
--
ALTER TABLE `kpi_tracking`
  ADD PRIMARY KEY (`id`),
  ADD KEY `kpi_setting_id` (`kpi_setting_id`);

--
-- Chỉ mục cho bảng `money_returns`
--
ALTER TABLE `money_returns`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_money_returns_created_at` (`created_at`),
  ADD KEY `idx_money_returns_customer_id` (`customer_id`),
  ADD KEY `idx_money_returns_pos_id` (`pos_id`),
  ADD KEY `fk_money_returns_order_id` (`order_id`);

--
-- Chỉ mục cho bảng `money_transfers`
--
ALTER TABLE `money_transfers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_money_transfers_order_id` (`order_id`),
  ADD KEY `idx_money_transfers_created_at` (`created_at`),
  ADD KEY `idx_money_transfers_transfer_date` (`transfer_date`);

--
-- Chỉ mục cho bảng `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_orders_user_id` (`user_id`),
  ADD KEY `idx_orders_card_id` (`card_id`),
  ADD KEY `idx_orders_created_at` (`created_at`);

--
-- Chỉ mục cho bảng `pos_terminals`
--
ALTER TABLE `pos_terminals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Chỉ mục cho bảng `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username_unique` (`username`);

--
-- Chỉ mục cho bảng `user_logs`
--
ALTER TABLE `user_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_logs_user_id` (`user_id`),
  ADD KEY `idx_user_logs_action` (`action`),
  ADD KEY `idx_user_logs_entity_type` (`entity_type`),
  ADD KEY `idx_user_logs_created_at` (`created_at`);

--
-- AUTO_INCREMENT cho các bảng đã đổ
--

--
-- AUTO_INCREMENT cho bảng `banks`
--
ALTER TABLE `banks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `cards`
--
ALTER TABLE `cards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `card_attachments`
--
ALTER TABLE `card_attachments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `card_swipes`
--
ALTER TABLE `card_swipes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `financial_reports`
--
ALTER TABLE `financial_reports`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `kpi_distribution_weights`
--
ALTER TABLE `kpi_distribution_weights`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `kpi_settings`
--
ALTER TABLE `kpi_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `kpi_tracking`
--
ALTER TABLE `kpi_tracking`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `money_returns`
--
ALTER TABLE `money_returns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `money_transfers`
--
ALTER TABLE `money_transfers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `pos_terminals`
--
ALTER TABLE `pos_terminals`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `user_logs`
--
ALTER TABLE `user_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Các ràng buộc cho các bảng đã đổ
--

--
-- Các ràng buộc cho bảng `cards`
--
ALTER TABLE `cards`
  ADD CONSTRAINT `cards_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `card_attachments`
--
ALTER TABLE `card_attachments`
  ADD CONSTRAINT `card_attachments_ibfk_1` FOREIGN KEY (`card_id`) REFERENCES `cards` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `card_swipes`
--
ALTER TABLE `card_swipes`
  ADD CONSTRAINT `card_swipes_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `card_swipes_ibfk_2` FOREIGN KEY (`card_id`) REFERENCES `cards` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `card_swipes_ibfk_3` FOREIGN KEY (`pos_id`) REFERENCES `pos_terminals` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `card_swipes_ibfk_4` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `financial_reports`
--
ALTER TABLE `financial_reports`
  ADD CONSTRAINT `financial_reports_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `kpi_distribution_weights`
--
ALTER TABLE `kpi_distribution_weights`
  ADD CONSTRAINT `kpi_distribution_weights_ibfk_1` FOREIGN KEY (`kpi_setting_id`) REFERENCES `kpi_settings` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `kpi_settings`
--
ALTER TABLE `kpi_settings`
  ADD CONSTRAINT `kpi_settings_ibfk_1` FOREIGN KEY (`parent_kpi_id`) REFERENCES `kpi_settings` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `kpi_tracking`
--
ALTER TABLE `kpi_tracking`
  ADD CONSTRAINT `kpi_tracking_ibfk_1` FOREIGN KEY (`kpi_setting_id`) REFERENCES `kpi_settings` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `money_returns`
--
ALTER TABLE `money_returns`
  ADD CONSTRAINT `fk_money_returns_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `money_returns_ibfk_1` FOREIGN KEY (`pos_id`) REFERENCES `pos_terminals` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `money_returns_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `money_returns_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `money_transfers`
--
ALTER TABLE `money_transfers`
  ADD CONSTRAINT `money_transfers_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `money_transfers_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `fk_orders_card` FOREIGN KEY (`card_id`) REFERENCES `cards` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `pos_terminals`
--
ALTER TABLE `pos_terminals`
  ADD CONSTRAINT `pos_terminals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Các ràng buộc cho bảng `user_logs`
--
ALTER TABLE `user_logs`
  ADD CONSTRAINT `fk_user_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
